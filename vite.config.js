import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'

// https://vite.dev/config/
export default defineConfig({
  // 为 Node.js 模块提供浏览器兼容的空实现
  define: {
    global: 'globalThis',
    'process.env.NODE_ENV': '"development"',
    // 内网部署：确保不会尝试访问外部资源
    __INTRANET_MODE__: true,
    __DISABLE_EXTERNAL_RESOURCES__: true,
    __OFFLINE_MODE__: true,
    // 禁用segment analytics在浏览器环境中的使用
    'process.env.SEGMENT_ANALYTICS_DISABLED': '"true"',
    // React 兼容性定义
    '__DEV__': 'false'
    // 移除 __REACT_DEVTOOLS_GLOBAL_HOOK__ 定义，避免与浏览器扩展冲突
  },
  
  // 解析配置 - 排除Node.js专用包
  resolve: {
    alias: {
      // 为Node.js专用包提供空实现
      '@segment/analytics-node': 'virtual:segment-mock',
      'node-fetch': false,
    }
  },

  plugins: [
    react(),
    // Module Federation已移除 - 使用nginx代理方案替代
    // 这样可以更好地隐藏真实OpenVSCode地址并提供更好的安全性
    visualizer({ open: false, filename: 'logs/stats.html' }),
    // 替换segment imports插件
    {
      name: 'replace-segment-imports',
             generateBundle(options, bundle) {
         for (const [, chunk] of Object.entries(bundle)) {
           if (chunk.type === 'chunk' && chunk.code) {
             // 替换所有segment imports
             chunk.code = chunk.code
               .replace(/import\s*"@segment\/analytics-node"\s*;?/g, '/* @segment/analytics-node removed */')
               .replace(/import\s*['"]@segment\/analytics-node['"]\s*;?/g, '/* @segment/analytics-node removed */')
               .replace(/from\s*['"]@segment\/analytics-node['"]/g, 'from "data:text/javascript,export default {};"');
           }
         }
      }
    }
  ],
  server: {
    host: '0.0.0.0',
    port: 5173,
    strictPort: false,
    open: false
  },
  build: {
    // 支持 Chrome 55+ 的兼容性目标
    target: ['es2015', 'chrome55'],
    minify: 'terser', // 使用terser压缩，更好的兼容性
    cssMinify: 'esbuild', // CSS 使用 esbuild 压缩
    modulePreload: false,
    sourcemap: false, // 生产环境关闭 sourcemap
    reportCompressedSize: process.env.BUILD_VERBOSE === 'true', // 可选择性显示压缩大小详情



    rollupOptions: {
      // 外部化 Node.js 模块，避免浏览器兼容性警告  
      external: ['@segment/analytics-node', 'node-fetch'],

      output: {
        // 优化的代码分割策略 - 更精细的分割
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            // React 生态系统 - 保持分离
            if (id.includes('react-dom')) return 'vendor-react-dom';
            if (id.includes('react/') && !id.includes('react-')) return 'vendor-react';
            if (id.includes('scheduler')) return 'vendor-react';

            // 图标库 - 按功能分割
            if (id.includes('react-icons/fa')) return 'vendor-icons-fa';
            if (id.includes('react-icons/hi')) return 'vendor-icons-hi';  
            if (id.includes('react-icons')) return 'vendor-icons-other';

            // 语法高亮 - 核心和语言包分离
            if (id.includes('highlight.js/lib/core')) return 'vendor-highlight-core';
            if (id.includes('highlight.js/lib/languages')) return 'vendor-highlight-langs';
            if (id.includes('highlight.js')) return 'vendor-highlight-core';

            // Markdown处理 - 按功能分组
            if (id.includes('react-markdown')) return 'vendor-markdown-react';
            if (id.includes('remark-') || id.includes('rehype-')) return 'vendor-markdown-plugins';
            if (id.includes('unified') || id.includes('micromark') || id.includes('mdast') || id.includes('hast')) {
              return 'vendor-markdown-core';
            }

            // HTTP 和网络库
            if (id.includes('axios')) return 'vendor-http';

            // 开发和构建工具库
            if (id.includes('vite') || id.includes('rollup') || id.includes('terser')) return 'vendor-build-tools';

            // CSS和样式处理库
            if (id.includes('tailwind') || id.includes('postcss') || id.includes('autoprefixer')) return 'vendor-styles';

            // polyfills
            if (id.includes('core-js') || id.includes('regenerator-runtime')) return 'vendor-polyfills';

            // 工具库
            if (id.includes('lodash') || id.includes('date-fns') || id.includes('uuid')) return 'vendor-utils';

            // AI SDK 相关
            if (id.includes('openai') || id.includes('anthropic')) return 'vendor-ai';

            // 其他小的第三方库
            return 'vendor-misc';
          }

          // 组件代码
          if (id.includes('src/components')) {
            if (id.includes('DocumentationSection')) return 'component-docs';
            if (id.includes('DownloadsSection')) return 'component-downloads';
            if (id.includes('WebIDESection')) return 'component-webide';
            if (id.includes('ChatSection')) return 'component-chat';
            return 'components-common';
          }

          // 服务层
          if (id.includes('src/services')) return 'services';

          // 工具类
          if (id.includes('src/utils')) return 'utils';
        },
        
        // 文件命名优化
        chunkFileNames: () => {
          return `assets/[name]-[hash].js`;
        },
        
        assetFileNames: (assetInfo) => {
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `assets/images/[name]-[hash][extname]`;
          }
          if (/\.(css)$/i.test(assetInfo.name)) {
            return `assets/css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    
    // 降低chunk大小警告阈值，推进更好的分割
    chunkSizeWarningLimit: 800, // 降低到 800KB，推进更好的性能
    
    // 资源内联阈值
    assetsInlineLimit: 8192, // 增加内联限制，减少小文件请求数量
    
    // 启用更激进的压缩
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: process.env.NODE_ENV === 'production',
        pure_funcs: ['console.log', 'console.info', 'console.warn'],
        passes: 2 // 多次压缩
      },
      mangle: {
        safari10: true // 确保Safari兼容性
      }
    }
  },
  
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      // 只预构建实际使用的图标包
      'react-icons/fa',
      'react-icons/hi',
      'axios',
      // 包含 polyfills 以确保正确处理
      'core-js/stable',
      'regenerator-runtime/runtime'
    ],
    exclude: [
      'openai', // 后端专用
      '@anthropic-ai/sdk', // 后端专用
      'express', // 后端专用
      'cors', // 后端专用
      'ldapjs', // 后端专用
      'node-fetch', // 后端专用
      'dotenv', // 后端专用
      'uuid', // 按需导入
      'concurrently', // 开发工具
      'react-router-dom', // 当前未使用
      '@segment/analytics-node', // Node.js 专用，避免浏览器构建警告
      '@segment/analytics-next',
      // 排除不常用的语法高亮语言包
      'prismjs/components',
      'highlight.js/lib/languages'
    ]
  },
  
  // CSS优化
  css: {
    devSourcemap: false,
    preprocessorOptions: {
      scss: {
        charset: false
      }
    },
    // 生产环境CSS优化 - 移动到postcss.config.js文件中处理
  },

  // 静态资源处理 - 支持现代图片格式
  assetsInclude: ['**/*.webp', '**/*.avif', '**/*.jpg', '**/*.jpeg', '**/*.png'],
  
  // 性能优化 - 为 Chrome 55 兼容性调整
  esbuild: {
    target: 'es2015', // 确保 esbuild 也使用兼容的目标
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
    legalComments: 'none'
  }
})
