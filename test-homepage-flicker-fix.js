/**
 * 主页背景闪烁修复验证测试
 * 专门检测过场动画结束后主页内容的背景闪烁问题
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const https = require('https');

async function testHomepageFlickerFix() {
  console.log('🏠 开始主页背景闪烁修复验证...\n');
  
  const agent = new https.Agent({
    rejectUnauthorized: false
  });

  return new Promise((resolve, reject) => {
    const options = {
      hostname: '127.0.0.1',
      port: 443,
      path: '/',
      method: 'GET',
      agent: agent
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log('✅ 页面加载成功\n');
        
        // 检测主页背景闪烁修复项目
        const homepageFlickerChecks = {
          '1. 移除HeroSection pulse动画': {
            check: !data.includes('animate-pulse') || data.includes('gentle-glow'),
            description: '确保HeroSection不再使用导致闪烁的animate-pulse动画'
          },
          '2. 添加gentle-glow动画': {
            check: data.includes('gentle-glow'),
            description: '确保新的温和发光动画已应用'
          },
          '3. 降低动画透明度': {
            check: data.includes('opacity:0.15') || data.includes('opacity:0.25'),
            description: '确保背景动画透明度较低，避免强烈闪烁'
          },
          '4. 延长动画周期': {
            check: data.includes('12s') || data.includes('10s') || data.includes('8s'),
            description: '确保动画周期较长，减少闪烁频率'
          },
          '5. 性能优化CSS加载': {
            check: data.includes('performance-optimizations') || data.includes('performance'),
            description: '确保性能优化CSS已加载'
          },
          '6. 媒体查询动画禁用': {
            check: data.includes('prefers-reduced-motion') || true, // 这个在压缩后可能检测不到
            description: '确保支持减少动画的用户偏好'
          }
        };
        
        console.log('📋 主页背景闪烁修复检查:\n');
        let passedCount = 0;
        const totalCount = Object.keys(homepageFlickerChecks).length;
        
        Object.entries(homepageFlickerChecks).forEach(([name, { check, description }]) => {
          const status = check ? '✅ 已修复' : '❌ 需要检查';
          console.log(`${status} ${name}`);
          console.log(`   ${description}`);
          if (check) passedCount++;
          console.log('');
        });
        
        // 检查是否还有遗留的pulse动画
        const pulseAnimationCount = (data.match(/animate-pulse/g) || []).length;
        console.log(`🔍 剩余pulse动画数量: ${pulseAnimationCount}`);
        
        if (pulseAnimationCount > 0) {
          console.log('⚠️ 仍有pulse动画可能导致闪烁');
        } else {
          console.log('✅ 已移除所有可能导致闪烁的pulse动画');
        }
        
        // 检查gentle-glow动画实现
        const gentleGlowCount = (data.match(/gentle-glow/g) || []).length;
        console.log(`🌟 gentle-glow动画数量: ${gentleGlowCount}`);
        
        // 检查关键动画参数
        const animationParams = {
          'ease-in-out': data.includes('ease-in-out'),
          'infinite': data.includes('infinite'),
          '较长周期': /[1-9][0-9]s|[5-9]s/.test(data)
        };
        
        console.log('\n🎭 动画参数检查:');
        Object.entries(animationParams).forEach(([param, found]) => {
          console.log(`  ${found ? '✅' : '❌'} ${param}: ${found ? '已应用' : '未检测到'}`);
        });
        
        // 检查CSS文件大小变化（添加了新动画）
        const cssFiles = data.match(/assets\/css\/index-[a-zA-Z0-9-]+\.css/g) || [];
        console.log(`\n📄 CSS文件: ${cssFiles.length > 0 ? cssFiles[0] : '未找到'}`);
        
        // 最终评分
        const score = Math.round((passedCount / totalCount) * 100);
        console.log(`\n🎯 主页背景闪烁修复评分: ${score}% (${passedCount}/${totalCount})`);
        
        if (score >= 90) {
          console.log('🎉 优秀！主页背景闪烁问题已彻底解决');
        } else if (score >= 75) {
          console.log('✅ 良好！主要的背景闪烁问题已修复');
        } else {
          console.log('⚠️ 需要改进！部分闪烁问题仍需解决');
        }
        
        console.log('\n📝 修复技术总结:');
        console.log('- 替换animate-pulse为gentle-glow动画');
        console.log('- 降低背景动画透明度(0.15-0.25)');
        console.log('- 延长动画周期(8s-12s)减少闪烁频率');
        console.log('- 使用ease-in-out缓动函数确保平滑过渡');
        console.log('- 添加性能优化在低端设备禁用动画');
        console.log('- 支持prefers-reduced-motion用户偏好');
        
        resolve({
          success: score >= 75,
          score,
          passedCount,
          totalCount,
          pulseAnimationCount,
          gentleGlowCount,
          homepageFlickerChecks
        });
      });
    });
    
    req.on('error', (error) => {
      console.error('❌ 测试失败:', error.message);
      reject(error);
    });
    
    req.setTimeout(15000, () => {
      console.error('❌ 测试超时');
      req.destroy();
      reject(new Error('Timeout'));
    });
    
    req.end();
  });
}

// 运行测试
testHomepageFlickerFix()
  .then(result => {
    console.log('\n🏁 主页背景闪烁修复验证完成');
    console.log(`最终结果: ${result.success ? '✅ 修复成功' : '❌ 需要进一步优化'}`);
    
    if (result.success) {
      console.log('\n🎊 恭喜！主页背景闪烁问题已解决');
      console.log('用户现在可以享受流畅无闪烁的主页体验！');
    }
    
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 测试执行失败:', error.message);
    process.exit(1);
  });