/**
 * 背景闪烁修复验证测试
 * 使用简单的JavaScript测试而不是Playwright
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const https = require('https');

async function testBackgroundFlicker() {
  console.log('🎯 开始背景闪烁修复验证测试...');
  
  const agent = new https.Agent({
    rejectUnauthorized: false // 忽略自签名证书
  });

  return new Promise((resolve, reject) => {
    const options = {
      hostname: '127.0.0.1',
      port: 443,
      path: '/',
      method: 'GET',
      agent: agent
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log('✅ 成功获取页面内容');
        
        // 检查关键的背景修复内容
        const checks = {
          htmlBackground: data.includes('html{background-color:#000!important'),
          bodyBackground: data.includes('background-color:#000!important'),
          rootBackground: data.includes('#root,body{background-color:#000!important'),
          loadingFallback: data.includes('background-color:#000!important;background:#000!important'),
          themeColor: data.includes('content="#000000"'),
          criticalCSS: data.includes('Critical CSS for initial paint')
        };
        
        console.log('\n📊 背景修复检查结果:');
        Object.entries(checks).forEach(([key, passed]) => {
          console.log(`  ${passed ? '✅' : '❌'} ${key}: ${passed ? '已修复' : '缺失'}`);
        });
        
        // 检查是否有潜在的白色背景
        const whiteBackgroundFound = data.match(/background.*white|bg-white/gi);
        if (whiteBackgroundFound) {
          console.log('\n⚠️ 发现潜在的白色背景:', whiteBackgroundFound);
        } else {
          console.log('\n✅ 未发现白色背景，修复有效');
        }
        
        // 统计修复项目
        const fixedCount = Object.values(checks).filter(Boolean).length;
        const totalChecks = Object.keys(checks).length;
        
        console.log(`\n🎉 修复进度: ${fixedCount}/${totalChecks} (${Math.round(fixedCount/totalChecks*100)}%)`);
        
        if (fixedCount === totalChecks) {
          console.log('✅ 所有背景闪烁修复项目都已成功应用！');
        } else {
          console.log('⚠️ 部分修复项目可能需要进一步检查');
        }
        
        // 检查HTML大小变化（增加了防闪烁代码）
        const htmlSize = data.length;
        console.log(`\n📏 HTML大小: ${htmlSize} 字节 (包含防闪烁CSS)`);
        
        resolve({
          success: fixedCount === totalChecks,
          fixedCount,
          totalChecks,
          htmlSize,
          checks
        });
      });
    });
    
    req.on('error', (error) => {
      console.error('❌ 测试失败:', error.message);
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      console.error('❌ 请求超时');
      req.destroy();
      reject(new Error('Timeout'));
    });
    
    req.end();
  });
}

// 运行测试
testBackgroundFlicker()
  .then(result => {
    console.log('\n🏁 测试完成');
    console.log('结果:', result.success ? '背景闪烁修复成功' : '需要进一步检查');
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 测试执行失败:', error.message);
    process.exit(1);
  });