# YNNX AI Platform 离线部署指南

## 概述

本指南提供了简化的 YNNX AI Platform 离线内网部署解决方案。通过一键打包脚本将应用完整打包，在目标环境解压后即可使用docker-compose启动。

## 部署架构

```
YNNX AI Platform
├── 前端应用 (React + Vite)
├── LDAP 认证服务 (Node.js + Express) 
├── OpenVSCode Web IDE + 扩展
├── LobeChat 智能对话
└── Nginx 反向代理
```

## 快速部署

### 第一步：源环境打包

在开发环境执行打包脚本：

```bash
# 确保应用当前运行正常
docker-compose up -d

# 执行打包（仅使用本地已有的Docker镜像）
./scripts/package-for-deployment.sh
```

打包完成后会生成：
- `ynnx-ai-platform-YYYYMMDD_HHMMSS.tar.gz` - 完整部署包
- `ynnx-ai-platform-YYYYMMDD_HHMMSS.tar.gz.sha256` - 校验和文件

### 第二步：上传到目标环境

```bash
# 上传部署包到目标服务器
scp ynnx-ai-platform-*.tar.gz* user@target-server:/opt/

# 登录目标服务器并解压
ssh user@target-server
cd /opt
tar -xzf ynnx-ai-platform-*.tar.gz
cd ynnx-ai-platform-*
```

### 第三步：执行部署

```bash
# 运行部署脚本（加载Docker镜像，恢复配置）
./deploy.sh

# 启动服务（无需构建，直接使用预打包的镜像）
cd app
docker-compose up -d
```

### 第四步：验证部署

```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

访问应用：
- 主页面: `https://服务器IP`
- VSCode IDE: `https://服务器IP/ide/`
- LobeChat: `https://服务器IP/chat/`

## 打包内容

### 应用文件
- `dist/` - 构建后的前端文件
- `src/` - 源代码
- `nginx/` - Nginx配置
- `docker-compose.yml` - 容器编排文件
- `.env` - 环境配置

### Docker镜像
- `app.tar` - 主应用镜像
- `nginx_alpine.tar` - Nginx镜像
- `gitpod_openvscode-server_latest.tar` - VSCode IDE镜像
- `lobehub_lobe-chat_latest.tar` - LobeChat镜像

### VSCode配置
- `openvscode-extensions/` - 已安装的扩展
- `openvscode-data/` - 用户数据和设置
- `openvscode-cache/` - 缓存文件
- `.openvscode/` - 工作区配置

## 系统要求

### 源环境
- Docker >= 20.10
- docker-compose >= 2.0
- Node.js >= 16.0
- 磁盘空间 >= 15GB
- 已有所需的Docker镜像（nginx:alpine, gitpod/openvscode-server, lobehub/lobe-chat）

### 目标环境
- Docker >= 20.10
- docker-compose >= 2.0
- 磁盘空间 >= 10GB
- 内存 >= 4GB
- 开放端口：80, 443, 3002, 3210, 3667, 8443

## 常用管理命令

```bash
# 进入应用目录
cd /opt/ynnx-ai-platform-*/app

# 查看服务状态
docker-compose ps

# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f nginx
docker-compose logs -f app

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新启动服务
docker-compose up -d
```

## 常见问题

**Q: 首次访问HTTPS显示证书警告？**
A: 这是正常现象，应用使用现有的SSL证书。点击"继续访问"即可。

**Q: VSCode扩展没有恢复？**
A: 检查文件权限，运行 `chown -R 1000:1000 openvscode-*`

**Q: 服务无法访问？**
A: 检查防火墙设置，确保必要端口已开放。

**Q: 如何更新应用？**
A: 重新执行打包和部署流程，新版本会覆盖旧版本。

**Q: 启动时提示无法连接Docker Hub？**
A: 这是正常现象，部署脚本已生成纯镜像版的docker-compose.yml，不需要网络构建。

## 备份恢复

### 备份关键数据
```bash
cd /opt/ynnx-ai-platform-*/app
tar -czf ../backup-$(date +%Y%m%d).tar.gz \
  openvscode-extensions/ \
  openvscode-data/ \
  .openvscode/ \
  .env \
  logs/
```

### 恢复数据
```bash
# 停止服务
docker-compose down

# 恢复数据
tar -xzf backup-*.tar.gz

# 重启服务
docker-compose up -d
```

---

**版本**: v1.0 (简化版)  
**更新时间**: 2025年9月  
**维护团队**: YNNX Development Team