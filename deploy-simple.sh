#!/bin/bash
# YNNX AI Platform 简化版部署脚本

set -euo pipefail

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOCKER_IMAGES_DIR="$PROJECT_DIR/.docker-images-temp"

echo "YNNX AI Platform 简化版部署脚本"
echo "================================"
echo "开始时间: $(date)"
echo "项目目录: $PROJECT_DIR"

# 检查Docker
echo "1. 检查Docker环境..."
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: docker-compose未安装"
    exit 1
fi

echo "✓ Docker环境检查通过"

# 加载Docker镜像
echo "2. 加载Docker镜像..."
if [[ -d "$DOCKER_IMAGES_DIR" ]]; then
    for image_file in "$DOCKER_IMAGES_DIR"/*.tar; do
        if [[ -f "$image_file" ]]; then
            echo "  加载镜像: $(basename "$image_file")"
            docker load -i "$image_file"
        fi
    done
    echo "✓ Docker镜像加载完成"
else
    echo "⚠️  警告: Docker镜像目录不存在，跳过镜像加载"
fi

# 创建必要目录
echo "3. 准备运行环境..."
cd "$PROJECT_DIR"
mkdir -p logs/{app,nginx}
mkdir -p openvscode-extensions openvscode-data openvscode-cache .openvscode

# 设置权限（如果是Linux环境）
if [[ "$OSTYPE" != "darwin"* ]]; then
    chown -R 1000:1000 openvscode-* .openvscode 2>/dev/null || true
fi

echo "✓ 运行环境准备完成"

echo
echo "======================================="
echo "部署准备完成！"
echo "======================================="
echo
echo "请执行以下命令启动服务:"
echo "  docker-compose up -d"
echo
echo "服务启动后可通过以下地址访问:"
local_ip=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "localhost")
echo "  主页面: https://$local_ip"
echo "  VSCode IDE: https://$local_ip/ide/"
echo "  LobeChat: https://$local_ip/chat/"
echo
echo "停止服务:"
echo "  docker-compose down"
echo
echo "查看服务状态:"
echo "  docker-compose ps"
echo "  docker-compose logs"
echo
echo "部署完成时间: $(date)"
