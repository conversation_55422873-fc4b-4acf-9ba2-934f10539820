<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>低版本浏览器兼容性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .iframe-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 20px;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>低版本浏览器兼容性测试</h1>
        <p>此页面用于测试应用在低版本浏览器中的兼容性</p>
        
        <h2>浏览器特性检测</h2>
        <div id="feature-tests"></div>
        
        <h2>模拟低版本浏览器</h2>
        <button onclick="simulateOldChrome()">模拟 Chrome 53</button>
        <button onclick="simulateOldFirefox()">模拟 Firefox 57</button>
        <button onclick="simulateOldSafari()">模拟 Safari 10</button>
        <button onclick="resetUserAgent()">重置 User Agent</button>
        
        <h2>应用预览</h2>
        <div class="iframe-container">
            <iframe id="app-frame" src="http://localhost:4173/"></iframe>
        </div>
    </div>

    <script>
        // 特性检测函数
        function runFeatureTests() {
            const tests = [
                {
                    name: 'Promise 支持',
                    test: () => typeof Promise !== 'undefined'
                },
                {
                    name: 'Fetch API 支持',
                    test: () => typeof fetch !== 'undefined'
                },
                {
                    name: 'Arrow Functions 支持',
                    test: () => {
                        try {
                            eval('(() => {})');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'Template Literals 支持',
                    test: () => {
                        try {
                            eval('`test`');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'Destructuring 支持',
                    test: () => {
                        try {
                            eval('const [a] = [1]');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'CSS Flexbox 支持',
                    test: () => {
                        const div = document.createElement('div');
                        return 'flex' in div.style || 'webkitFlex' in div.style;
                    }
                },
                {
                    name: 'CSS Grid 支持',
                    test: () => {
                        const div = document.createElement('div');
                        return 'grid' in div.style || 'msGrid' in div.style;
                    }
                },
                {
                    name: 'LocalStorage 支持',
                    test: () => {
                        try {
                            return typeof localStorage !== 'undefined' && localStorage !== null;
                        } catch (e) {
                            return false;
                        }
                    }
                }
            ];

            const container = document.getElementById('feature-tests');
            container.innerHTML = '';

            tests.forEach(test => {
                const result = test.test();
                const div = document.createElement('div');
                div.className = 'test-result ' + (result ? 'pass' : 'fail');
                div.textContent = test.name + ': ' + (result ? '✓ 支持' : '✗ 不支持');
                container.appendChild(div);
            });
        }

        // 模拟不同浏览器的 User Agent
        function simulateOldChrome() {
            // Chrome 53 User Agent
            const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.143 Safari/537.36';
            updateUserAgent(userAgent, 'Chrome 53');
        }

        function simulateOldFirefox() {
            // Firefox 57 User Agent
            const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:57.0) Gecko/20100101 Firefox/57.0';
            updateUserAgent(userAgent, 'Firefox 57');
        }

        function simulateOldSafari() {
            // Safari 10 User Agent
            const userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/603.3.8 (KHTML, like Gecko) Version/10.1.2 Safari/603.3.8';
            updateUserAgent(userAgent, 'Safari 10.1');
        }

        function resetUserAgent() {
            updateUserAgent(navigator.userAgent, '当前浏览器');
        }

        function updateUserAgent(userAgent, browserName) {
            // 注意：实际上无法修改 navigator.userAgent，这里只是演示
            console.log('模拟浏览器:', browserName);
            console.log('User Agent:', userAgent);
            
            // 重新加载 iframe 以测试兼容性
            const iframe = document.getElementById('app-frame');
            iframe.src = iframe.src;
            
            // 显示提示
            const div = document.createElement('div');
            div.className = 'test-result warning';
            div.textContent = '已模拟 ' + browserName + '，请查看控制台和应用表现';
            document.getElementById('feature-tests').appendChild(div);
        }

        // 页面加载时运行测试
        window.onload = function() {
            runFeatureTests();
        };
    </script>
</body>
</html>
