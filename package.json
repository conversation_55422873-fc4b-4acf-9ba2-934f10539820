{"name": "ynnx-ai-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "NODE_ENV=development vite", "dev:host": "vite --host 0.0.0.0", "build": "NODE_ENV=production vite build --mode production && node scripts/optimize-critical-path.js && node scripts/replace-external-links.cjs", "build:verbose": "BUILD_VERBOSE=true NODE_ENV=production vite build --mode production && node scripts/optimize-critical-path.js && node scripts/replace-external-links.cjs", "build:staging": "NODE_ENV=staging vite build", "build:intranet": "node scripts/build-intranet.cjs", "verify:intranet": "node scripts/verify-intranet-deployment.cjs", "lint": "eslint .", "preview": "vite preview", "preview:host": "vite preview --host 0.0.0.0", "ldap": "NODE_ENV=development node scripts/start-ldap.js", "dev:full": "concurrently \"npm run dev\" \"npm run ldap\"", "dev:network": "concurrently \"npm run dev:host\" \"npm run ldap\"", "config:check": "node scripts/check-hardcoded-config.cjs", "config:validate": "node scripts/validate-env-config.cjs", "config:setup": "cp env.example .env && echo '✅ 已创建 .env 文件，请根据实际环境修改配置值'", "intranet:check": "node scripts/check-external-dependencies.cjs", "intranet:prepare": "node scripts/prepare-intranet-deployment.cjs", "intranet:localize": "node scripts/localize-external-resources.cjs", "intranet:restore": "node scripts/restore-original-files.cjs", "intranet:restore-localized": "node scripts/restore-localized-resources.cjs", "check:external": "npm run intranet:check", "optimize": "node scripts/run-all-optimizations.js", "optimize:images": "node scripts/optimize-images.cjs", "optimize:build": "npm run optimize && npm run build", "test:compatibility": "node scripts/run-compatibility-tests.js", "test:compatibility:chrome": "node scripts/run-compatibility-tests.js --project=\"Chrome Latest\"", "test:compatibility:firefox": "node scripts/run-compatibility-tests.js --project=\"Firefox Latest\"", "test:compatibility:safari": "node scripts/run-compatibility-tests.js --project=\"Safari Latest\"", "test:compatibility:mobile": "npx playwright test --project=\"Mobile Chrome\" --project=\"Mobile Safari\"", "test:compatibility:legacy": "npx playwright test --project=\"Chrome 50\" --project=\"Firefox 52\" --project=\"Safari 10\" --project=\"Edge 15\"", "test:chrome:all": "npx playwright test --project=\"Chrome 50\" --project=\"Chrome 60\" --project=\"Chrome 70\" --project=\"Chrome 80\" --project=\"Chrome 90\" --project=\"Chrome 100\" --project=\"Chrome Latest\"", "test:chrome:versions": "npx playwright test tests/chrome-versions-compatibility.spec.js", "test:chrome:50": "npx playwright test --project=\"Chrome 50\" tests/chrome-versions-compatibility.spec.js", "test:chrome:60": "npx playwright test --project=\"Chrome 60\" tests/chrome-versions-compatibility.spec.js", "test:chrome:70": "npx playwright test --project=\"Chrome 70\" tests/chrome-versions-compatibility.spec.js", "test:chrome:80": "npx playwright test --project=\"Chrome 80\" tests/chrome-versions-compatibility.spec.js", "test:chrome:90": "npx playwright test --project=\"Chrome 90\" tests/chrome-versions-compatibility.spec.js", "test:chrome:100": "npx playwright test --project=\"Chrome 100\" tests/chrome-versions-compatibility.spec.js", "test:chrome:evolution": "npx playwright test --grep=\"演进对比\" tests/chrome-versions-compatibility.spec.js", "analyze:chrome": "node scripts/chrome-compatibility-analyzer.js", "test:playwright": "npx playwright test", "test:playwright:headed": "npx playwright test --headed", "test:playwright:debug": "npx playwright test --debug"}, "dependencies": {"@anthropic-ai/sdk": "^0.17.0", "axios": "^1.9.0", "concurrently": "^8.2.2", "core-js": "^3.45.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "highlight.js": "^11.11.1", "ldapjs": "^3.0.7", "node-fetch": "^3.3.2", "openai": "^4.28.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-markdown": "10.1.0", "regenerator-runtime": "^0.14.1", "rehype-highlight": "7.0.2", "rehype-raw": "7.0.0", "remark-gfm": "4.0.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@playwright/test": "^1.54.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "cssnano": "^7.1.0", "cssnano-preset-advanced": "^7.0.8", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "postcss-combine-media-query": "^2.0.0", "rollup-plugin-visualizer": "6.0.3", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "vite": "^6.3.5"}, "browserslist": ["Chrome >= 55", "Firefox >= 52", "Safari >= 10", "Edge >= 15", "IE >= 11", "> 1%", "not dead"]}