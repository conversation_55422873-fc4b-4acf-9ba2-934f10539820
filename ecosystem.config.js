module.exports = {
  apps: [
    {
      name: 'ynnx-ldap-auth',
      script: 'src/server/ldapAuthServer.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        LDAP_AUTH_PORT: 3002
      },
      env_production: {
        NODE_ENV: 'production',
        LDAP_AUTH_PORT: 3002
      },
      log_file: '/var/log/ynnx-ai/ldap-auth.log',
      error_file: '/var/log/ynnx-ai/ldap-error.log',
      max_memory_restart: '300M',
      min_uptime: '30s',          // 增加最小运行时间
      max_restarts: 15,           // 15分钟内最多重启15次
      restart_delay: 1000,        // 重启延迟1秒
      autorestart: true,          // 确保自动重启
      watch: false,               // 禁用文件监视避免不必要的重启
      ignore_watch: ['node_modules', 'logs'],
      exp_backoff_restart_delay: 100  // 指数退避重启延迟
    }
  ],
  
  deploy: {
    production: {
      user: process.env.DEPLOY_USER || 'ynnx',
      host: process.env.DEPLOY_HOSTS ? process.env.DEPLOY_HOSTS.split(',') : ['server1.ynnx.com', 'server2.ynnx.com'],
      ref: 'origin/main',
      repo: process.env.DEPLOY_REPO || '**************:ynnx/ynnx-ai-platform.git',
      path: '/var/www/ynnx-ai-platform',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
}; 