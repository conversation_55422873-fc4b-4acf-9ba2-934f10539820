import { test, expect } from '@playwright/test';

// 响应式设计兼容性测试
test.describe('响应式设计兼容性测试', () => {
  
  const viewports = [
    { name: '手机竖屏', width: 375, height: 667 },      // iPhone SE
    { name: '手机横屏', width: 667, height: 375 },      // iPhone SE 横屏
    { name: '平板竖屏', width: 768, height: 1024 },     // iPad
    { name: '平板横屏', width: 1024, height: 768 },     // iPad 横屏
    { name: '小笔记本', width: 1366, height: 768 },     // 常见笔记本分辨率
    { name: '桌面显示器', width: 1920, height: 1080 },  // Full HD
    { name: '2K显示器', width: 2560, height: 1440 },    // 2K
    { name: '4K显示器', width: 3840, height: 2160 }     // 4K
  ];
  
  viewports.forEach(viewport => {
    test(`响应式布局测试 - ${viewport.name} (${viewport.width}x${viewport.height})`, async ({ page, browserName }) => {
      // 设置视口大小
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // 等待布局调整
      await page.waitForTimeout(1000);
      
      // 检查基本布局响应
      const layoutInfo = await page.evaluate(() => {
        const body = document.body;
        const main = document.querySelector('main') || document.querySelector('#root') || body;
        
        return {
          bodyWidth: body.clientWidth,
          bodyHeight: body.clientHeight,
          mainWidth: main.clientWidth,
          mainHeight: main.clientHeight,
          scrollWidth: body.scrollWidth,
          scrollHeight: body.scrollHeight,
          hasHorizontalScroll: body.scrollWidth > body.clientWidth,
          hasVerticalScroll: body.scrollHeight > body.clientHeight
        };
      });
      
      console.log(`${viewport.name} 布局信息 (${browserName}):`, layoutInfo);
      
      // 验证基本响应式要求
      expect(layoutInfo.bodyWidth).toBeLessThanOrEqual(viewport.width);
      
      // 小屏幕不应该有横向滚动条（除非内容确实需要）
      if (viewport.width <= 768) {
        // 允许少量的横向溢出（可能是由于滚动条或边距）
        expect(layoutInfo.scrollWidth - layoutInfo.bodyWidth).toBeLessThan(50);
      }
      
      // 检查导航菜单的响应式行为
      const navInfo = await page.evaluate(() => {
        const nav = document.querySelector('nav') || document.querySelector('[role="navigation"]');
        const menuButton = document.querySelector('[aria-label="menu"]') || 
                          document.querySelector('[data-testid="menu-button"]') ||
                          document.querySelector('button[type="button"]');
        
        if (!nav) return { found: false };
        
        const style = getComputedStyle(nav);
        return {
          found: true,
          display: style.display,
          visibility: style.visibility,
          opacity: style.opacity,
          transform: style.transform,
          hasMenuButton: !!menuButton,
          menuButtonVisible: menuButton ? getComputedStyle(menuButton).display !== 'none' : false
        };
      });
      
      if (navInfo.found) {
        console.log(`${viewport.name} 导航信息:`, navInfo);
      }
      
      // 检查文字可读性
      const textInfo = await page.evaluate(() => {
        const elements = document.querySelectorAll('h1, h2, h3, p, span, div');
        const textSizes = [];
        const lineHeights = [];
        
        elements.forEach(el => {
          const style = getComputedStyle(el);
          const fontSize = parseFloat(style.fontSize);
          const lineHeight = parseFloat(style.lineHeight);
          
          if (fontSize > 0 && el.textContent.trim().length > 0) {
            textSizes.push(fontSize);
            if (!isNaN(lineHeight)) {
              lineHeights.push(lineHeight);
            }
          }
        });
        
        return {
          minFontSize: Math.min(...textSizes),
          maxFontSize: Math.max(...textSizes),
          avgFontSize: textSizes.length > 0 ? textSizes.reduce((a, b) => a + b) / textSizes.length : 0,
          minLineHeight: Math.min(...lineHeights),
          maxLineHeight: Math.max(...lineHeights)
        };
      });
      
      console.log(`${viewport.name} 文字信息:`, textInfo);
      
      // 移动设备上文字不应该太小
      if (viewport.width <= 768) {
        expect(textInfo.minFontSize).toBeGreaterThanOrEqual(14);
      }
      
      // 检查触摸目标大小（移动设备）
      if (viewport.width <= 768) {
        const touchTargets = await page.evaluate(() => {
          const interactiveElements = document.querySelectorAll('button, a, input, [onclick], [role="button"]');
          const targets = [];
          
          interactiveElements.forEach(el => {
            const rect = el.getBoundingClientRect();
            targets.push({
              width: rect.width,
              height: rect.height,
              area: rect.width * rect.height,
              tag: el.tagName.toLowerCase()
            });
          });
          
          return targets;
        });
        
        console.log(`${viewport.name} 触摸目标数量:`, touchTargets.length);
        
        // 触摸目标应该足够大（至少44x44px，这是iOS和Android的推荐尺寸）
        const smallTargets = touchTargets.filter(target => 
          target.width < 44 || target.height < 44
        );
        
        console.log(`${viewport.name} 过小的触摸目标:`, smallTargets.length);
      }
      
      // 检查图片响应式
      const imageInfo = await page.evaluate(() => {
        const images = document.querySelectorAll('img');
        const imageData = [];
        
        images.forEach(img => {
          const rect = img.getBoundingClientRect();
          imageData.push({
            src: img.src,
            width: rect.width,
            height: rect.height,
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight,
            isResponsive: img.style.maxWidth === '100%' || 
                          getComputedStyle(img).maxWidth === '100%',
            aspectRatio: img.naturalWidth / img.naturalHeight
          });
        });
        
        return imageData;
      });
      
      console.log(`${viewport.name} 图片数量:`, imageInfo.length);
      
      // 检查视频响应式
      const videoInfo = await page.evaluate(() => {
        const videos = document.querySelectorAll('video, iframe[src*="youtube"], iframe[src*="vimeo"]');
        return Array.from(videos).map(video => {
          const rect = video.getBoundingClientRect();
          return {
            type: video.tagName.toLowerCase(),
            width: rect.width,
            height: rect.height,
            aspectRatio: rect.width / rect.height
          };
        });
      });
      
      if (videoInfo.length > 0) {
        console.log(`${viewport.name} 视频/媒体:`, videoInfo);
      }
      
      // 检查表格响应式
      const tableInfo = await page.evaluate(() => {
        const tables = document.querySelectorAll('table');
        return Array.from(tables).map(table => {
          const rect = table.getBoundingClientRect();
          const parentRect = table.parentElement.getBoundingClientRect();
          return {
            width: rect.width,
            parentWidth: parentRect.width,
            overflowsParent: rect.width > parentRect.width,
            columns: table.querySelectorAll('th, td').length / (table.querySelectorAll('tr').length || 1)
          };
        });
      });
      
      if (tableInfo.length > 0) {
        console.log(`${viewport.name} 表格信息:`, tableInfo);
      }
      
      // 截图用于视觉比较
      await page.screenshot({
        path: `test-results/responsive-${viewport.name.replace(/\s+/g, '-')}-${browserName}.png`,
        fullPage: false
      });
      
      // 性能检查 - 在不同视口大小下的渲染性能
      const performanceMetrics = await page.evaluate(() => {
        if (typeof performance !== 'undefined' && performance.getEntriesByType) {
          const paintEntries = performance.getEntriesByType('paint');
          const layoutEntries = performance.getEntriesByType('layout-shift') || [];
          
          return {
            firstPaint: paintEntries.find(entry => entry.name === 'first-paint')?.startTime || 0,
            firstContentfulPaint: paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0,
            layoutShifts: layoutEntries.length,
            memoryUsage: performance.memory ? {
              used: performance.memory.usedJSHeapSize,
              total: performance.memory.totalJSHeapSize,
              limit: performance.memory.jsHeapSizeLimit
            } : null
          };
        }
        return null;
      });
      
      if (performanceMetrics) {
        console.log(`${viewport.name} 性能指标:`, performanceMetrics);
      }
    });
  });
  
  // 特殊测试：极端视口大小
  test('极端视口大小测试', async ({ page, browserName }) => {
    const extremeViewports = [
      { name: '极窄屏幕', width: 320, height: 568 },    // iPhone 5/SE
      { name: '极宽屏幕', width: 3440, height: 1440 },  // 21:9 超宽屏
      { name: '极小屏幕', width: 240, height: 320 },    // 功能手机
      { name: '正方形屏幕', width: 1024, height: 1024 } // 正方形显示器
    ];
    
    for (const viewport of extremeViewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(500);
      
      const layoutHealth = await page.evaluate(() => {
        const body = document.body;
        const issues = [];
        
        // 检查是否有元素溢出
        if (body.scrollWidth > body.clientWidth + 10) {
          issues.push('horizontal-overflow');
        }
        
        // 检查是否有元素被完全隐藏
        const hiddenElements = document.querySelectorAll('[style*="display: none"]').length;
        if (hiddenElements > 0) {
          issues.push(`${hiddenElements}-hidden-elements`);
        }
        
        // 检查是否有重叠元素
        const elements = document.querySelectorAll('*');
        let overlapping = 0;
        for (let i = 0; i < Math.min(elements.length, 50); i++) {
          const rect = elements[i].getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0) {
            for (let j = i + 1; j < Math.min(elements.length, 50); j++) {
              const otherRect = elements[j].getBoundingClientRect();
              if (rect.left < otherRect.right && rect.right > otherRect.left &&
                  rect.top < otherRect.bottom && rect.bottom > otherRect.top) {
                overlapping++;
              }
            }
          }
        }
        if (overlapping > 10) {
          issues.push('excessive-overlapping');
        }
        
        return {
          issues,
          bodySize: { width: body.clientWidth, height: body.clientHeight },
          scrollSize: { width: body.scrollWidth, height: body.scrollHeight }
        };
      });
      
      console.log(`${viewport.name} 布局健康度:`, layoutHealth);
      
      // 极端视口下也不应该有严重的布局问题
      expect(layoutHealth.issues).not.toContain('excessive-overlapping');
      
      await page.screenshot({
        path: `test-results/extreme-${viewport.name.replace(/\s+/g, '-')}-${browserName}.png`,
        fullPage: false
      });
    }
  });
  
  // 设备像素比测试
  test('高密度显示器兼容性测试', async ({ page, browserName }) => {
    const devicePixelRatios = [1, 1.5, 2, 3];
    
    for (const ratio of devicePixelRatios) {
      await page.emulateMedia({ reducedMotion: 'reduce' }); // 减少动画影响
      
      await page.addInitScript((dpr) => {
        Object.defineProperty(window, 'devicePixelRatio', {
          value: dpr,
          writable: false
        });
      }, ratio);
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const dprInfo = await page.evaluate(() => {
        return {
          devicePixelRatio: window.devicePixelRatio,
          screenWidth: screen.width,
          screenHeight: screen.height,
          innerWidth: window.innerWidth,
          innerHeight: window.innerHeight
        };
      });
      
      console.log(`DPR ${ratio} 显示信息 (${browserName}):`, dprInfo);
      
      // 检查图片是否有高分辨率版本
      const imageQuality = await page.evaluate(() => {
        const images = document.querySelectorAll('img');
        let highDpiImages = 0;
        
        images.forEach(img => {
          if (img.srcset || img.getAttribute('data-srcset') || 
              img.src.includes('@2x') || img.src.includes('_2x')) {
            highDpiImages++;
          }
        });
        
        return {
          total: images.length,
          highDpi: highDpiImages,
          percentage: images.length > 0 ? (highDpiImages / images.length) * 100 : 0
        };
      });
      
      console.log(`DPR ${ratio} 高清图片支持:`, imageQuality);
    }
  });
});