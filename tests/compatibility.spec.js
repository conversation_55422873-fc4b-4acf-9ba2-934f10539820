import { test, expect } from '@playwright/test';

// 兼容性测试套件
test.describe('Chrome 版本兼容性测试', () => {
  
  // 基础页面加载测试
  test('页面能够正常加载', async ({ page, browserName }) => {
    console.log(`测试浏览器: ${browserName}`);
    
    // 监听控制台错误
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // 监听页面错误
    const pageErrors = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
    });
    
    // 访问主页
    await page.goto('/');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 检查页面标题
    await expect(page).toHaveTitle(/YNNX|AI/i);
    
    // 检查是否有致命错误
    if (pageErrors.length > 0) {
      console.warn(`页面错误 (${browserName}):`, pageErrors);
    }
    
    if (consoleErrors.length > 0) {
      console.warn(`控制台错误 (${browserName}):`, consoleErrors);
    }
    
    // 页面应该能够加载基本内容
    await expect(page.locator('body')).toBeVisible();
  });
  
  // React 组件渲染测试
  test('React 组件能够正常渲染', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 等待 React 应用加载
    await page.waitForFunction(() => {
      return document.querySelector('#root') && 
             document.querySelector('#root').children.length > 0;
    }, { timeout: 10000 });
    
    // 检查主要组件是否存在
    const mainContent = page.locator('#root > *');
    await expect(mainContent).toBeVisible();
    
    console.log(`✅ React 组件渲染正常 (${browserName})`);
  });
  
  // JavaScript 功能测试
  test('JavaScript 基础功能正常工作', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 测试基础 JavaScript 功能
    const jsFeatures = await page.evaluate(() => {
      const features = {};
      
      // 测试 Promise 支持
      features.promiseSupport = typeof Promise !== 'undefined';
      
      // 测试 Arrow functions (通过字符串检查)
      try {
        new Function('return () => {}')();
        features.arrowFunctions = true;
      } catch (e) {
        features.arrowFunctions = false;
      }
      
      // 测试 const/let 支持
      try {
        new Function('const x = 1; let y = 2;')();
        features.constLet = true;
      } catch (e) {
        features.constLet = false;
      }
      
      // 测试 Array.includes
      features.arrayIncludes = typeof Array.prototype.includes === 'function';
      
      // 测试 Object.assign
      features.objectAssign = typeof Object.assign === 'function';
      
      // 测试 fetch API
      features.fetchAPI = typeof fetch === 'function';
      
      return features;
    });
    
    console.log(`JavaScript 功能支持情况 (${browserName}):`, jsFeatures);
    
    // 基础功能应该都支持（由于我们添加了 polyfills）
    expect(jsFeatures.promiseSupport).toBe(true);
    expect(jsFeatures.arrayIncludes).toBe(true);
    expect(jsFeatures.objectAssign).toBe(true);
    
    console.log(`✅ JavaScript 基础功能正常 (${browserName})`);
  });
  
  // CSS 样式和布局测试
  test('CSS 样式和布局正常显示', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 等待样式加载
    await page.waitForFunction(() => {
      const computed = window.getComputedStyle(document.body);
      return computed.backgroundColor !== 'rgba(0, 0, 0, 0)' || 
             computed.backgroundColor !== 'transparent';
    }, { timeout: 5000 });
    
    // 检查页面基本布局
    const bodyStyles = await page.evaluate(() => {
      const computed = window.getComputedStyle(document.body);
      return {
        backgroundColor: computed.backgroundColor,
        fontFamily: computed.fontFamily,
        fontSize: computed.fontSize
      };
    });
    
    console.log(`页面样式 (${browserName}):`, bodyStyles);
    
    // 检查是否应用了基本样式
    expect(bodyStyles.fontFamily).not.toBe('');
    
    console.log(`✅ CSS 样式正常 (${browserName})`);
  });
  
  // 交互功能测试
  test('用户交互功能正常', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 等待页面完全加载
    await page.waitForTimeout(2000);
    
    // 查找可点击的元素（按钮、链接等）
    const clickableElements = await page.locator('button, a, [role="button"], [onclick]').count();
    console.log(`发现 ${clickableElements} 个可交互元素 (${browserName})`);
    
    // 测试页面滚动
    await page.evaluate(() => window.scrollTo(0, 100));
    await page.waitForTimeout(500);
    
    const scrollPosition = await page.evaluate(() => window.pageYOffset);
    expect(scrollPosition).toBeGreaterThan(0);
    
    console.log(`✅ 用户交互功能正常 (${browserName})`);
  });
  
  // API 兼容性测试
  test('现代 Web API 兼容性检查', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const apiSupport = await page.evaluate(() => {
      return {
        // Storage APIs
        localStorage: typeof localStorage !== 'undefined',
        sessionStorage: typeof sessionStorage !== 'undefined',
        
        // Modern APIs (可能需要 polyfills)
        intersectionObserver: typeof IntersectionObserver !== 'undefined',
        resizeObserver: typeof ResizeObserver !== 'undefined',
        mutationObserver: typeof MutationObserver !== 'undefined',
        
        // Network APIs
        fetch: typeof fetch !== 'undefined',
        
        // ES6+ Features (通过 polyfills 支持)
        map: typeof Map !== 'undefined',
        set: typeof Set !== 'undefined',
        weakMap: typeof WeakMap !== 'undefined',
        
        // Async/Await support check
        asyncAwait: (async () => true) instanceof Promise
      };
    });
    
    console.log(`API 支持情况 (${browserName}):`, apiSupport);
    
    // 基础 APIs 应该都支持
    expect(apiSupport.localStorage).toBe(true);
    expect(apiSupport.sessionStorage).toBe(true);
    expect(apiSupport.fetch).toBe(true);
    expect(apiSupport.map).toBe(true);
    expect(apiSupport.set).toBe(true);
    
    console.log(`✅ Web APIs 兼容性正常 (${browserName})`);
  });
  
  // 性能测试
  test('页面加载性能测试', async ({ page, browserName }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    console.log(`页面加载时间: ${loadTime}ms (${browserName})`);
    
    // 页面应该在合理时间内加载完成（30秒内）
    expect(loadTime).toBeLessThan(30000);
    
    // 检查页面大小
    const metrics = await page.evaluate(() => ({
      documentReadyState: document.readyState,
      performanceTiming: performance.timing ? {
        loadEventEnd: performance.timing.loadEventEnd,
        navigationStart: performance.timing.navigationStart
      } : null
    }));
    
    console.log(`性能指标 (${browserName}):`, metrics);
    expect(metrics.documentReadyState).toBe('complete');
    
    console.log(`✅ 性能测试通过 (${browserName})`);
  });
  
});