import { test, expect } from '@playwright/test';

// 跨浏览器交互兼容性测试
test.describe('跨浏览器交互兼容性测试', () => {
  
  // 鼠标事件兼容性测试
  test('鼠标事件兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 查找可交互元素
    const interactiveElements = await page.locator('button, a, [role="button"], [onclick]').all();
    
    if (interactiveElements.length > 0) {
      const element = interactiveElements[0];
      
      // 测试各种鼠标事件
      const mouseEvents = await page.evaluate(() => {
        const events = [];
        const testElement = document.querySelector('button, a, [role="button"], [onclick]');
        
        if (!testElement) return { error: 'No interactive element found' };
        
        // 添加事件监听器
        const eventTypes = ['mousedown', 'mouseup', 'click', 'mouseover', 'mouseout', 'mouseenter', 'mouseleave'];
        
        eventTypes.forEach(eventType => {
          testElement.addEventListener(eventType, (e) => {
            events.push({
              type: e.type,
              button: e.button,
              buttons: e.buttons,
              clientX: e.clientX,
              clientY: e.clientY,
              target: e.target.tagName
            });
          });
        });
        
        // 模拟鼠标事件序列
        const rect = testElement.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        // 创建并分发鼠标事件
        const mouseoverEvent = new MouseEvent('mouseover', {
          view: window,
          bubbles: true,
          cancelable: true,
          clientX: centerX,
          clientY: centerY
        });
        
        const mousedownEvent = new MouseEvent('mousedown', {
          view: window,
          bubbles: true,
          cancelable: true,
          button: 0,
          buttons: 1,
          clientX: centerX,
          clientY: centerY
        });
        
        const mouseupEvent = new MouseEvent('mouseup', {
          view: window,
          bubbles: true,
          cancelable: true,
          button: 0,
          buttons: 0,
          clientX: centerX,
          clientY: centerY
        });
        
        const clickEvent = new MouseEvent('click', {
          view: window,
          bubbles: true,
          cancelable: true,
          button: 0,
          buttons: 0,
          clientX: centerX,
          clientY: centerY
        });
        
        testElement.dispatchEvent(mouseoverEvent);
        testElement.dispatchEvent(mousedownEvent);
        testElement.dispatchEvent(mouseupEvent);
        testElement.dispatchEvent(clickEvent);
        
        return { events, elementTag: testElement.tagName };
      });
      
      console.log(`鼠标事件测试结果 (${browserName}):`, mouseEvents);
      
      if (!mouseEvents.error) {
        // 验证基本事件序列
        const eventTypes = mouseEvents.events.map(e => e.type);
        expect(eventTypes).toContain('click');
      }
    }
  });
  
  // 键盘事件兼容性测试
  test('键盘事件兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 查找输入元素
    const inputElements = await page.locator('input, textarea, [contenteditable]').all();
    
    if (inputElements.length > 0) {
      const input = inputElements[0];
      
      await input.focus();
      
      const keyboardEvents = await page.evaluate(() => {
        const events = [];
        const testElement = document.querySelector('input, textarea, [contenteditable]') || document.body;
        
        // 添加键盘事件监听器
        const keyEventTypes = ['keydown', 'keypress', 'keyup', 'input'];
        
        keyEventTypes.forEach(eventType => {
          testElement.addEventListener(eventType, (e) => {
            events.push({
              type: e.type,
              key: e.key,
              keyCode: e.keyCode,
              which: e.which,
              code: e.code,
              ctrlKey: e.ctrlKey,
              shiftKey: e.shiftKey,
              altKey: e.altKey,
              metaKey: e.metaKey
            });
          });
        });
        
        return { listening: true, elementTag: testElement.tagName };
      });
      
      // 测试各种按键
      const keysToTest = ['Enter', 'Tab', 'Escape', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
      
      for (const key of keysToTest) {
        await page.keyboard.press(key);
        await page.waitForTimeout(50);
      }
      
      // 测试组合键
      await page.keyboard.press('Control+a');
      await page.keyboard.press('Control+c');
      
      const finalEvents = await page.evaluate(() => {
        return window.keyboardTestEvents || [];
      });
      
      console.log(`键盘事件兼容性 (${browserName}): 监听设置成功`);
    }
  });
  
  // 触摸事件兼容性测试（模拟移动设备）
  test('触摸事件兼容性', async ({ page, browserName }) => {
    // 模拟触摸设备
    await page.emulateMedia({ reducedMotion: 'reduce' });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const touchSupport = await page.evaluate(() => {
      const support = {
        touchEvents: 'ontouchstart' in window,
        touchScreen: 'maxTouchPoints' in navigator && navigator.maxTouchPoints > 0,
        pointerEvents: 'onpointerdown' in window,
        touchActionCSS: CSS.supports('touch-action', 'manipulation')
      };
      
      // 测试触摸事件创建
      try {
        const touchEvent = new TouchEvent('touchstart', {
          bubbles: true,
          cancelable: true,
          touches: [],
          targetTouches: [],
          changedTouches: []
        });
        support.canCreateTouchEvents = true;
      } catch (e) {
        support.canCreateTouchEvents = false;
        support.touchEventError = e.message;
      }
      
      return support;
    });
    
    console.log(`触摸事件支持 (${browserName}):`, touchSupport);
    
    // 查找可触摸元素并测试
    const touchElements = await page.locator('button, a, [role="button"]').all();
    
    if (touchElements.length > 0 && touchSupport.touchEvents) {
      const element = touchElements[0];
      
      // 模拟触摸交互
      const boundingBox = await element.boundingBox();
      if (boundingBox) {
        const x = boundingBox.x + boundingBox.width / 2;
        const y = boundingBox.y + boundingBox.height / 2;
        
        // 模拟触摸序列
        await page.touchscreen.tap(x, y);
        
        console.log(`触摸交互测试完成 (${browserName})`);
      }
    }
  });
  
  // 表单交互兼容性测试
  test('表单交互兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 查找表单元素
    const formElements = await page.evaluate(() => {
      const forms = document.querySelectorAll('form');
      const inputs = document.querySelectorAll('input, textarea, select');
      
      const formData = Array.from(forms).map((form, index) => ({
        index,
        action: form.action,
        method: form.method,
        elements: form.elements.length
      }));
      
      const inputData = Array.from(inputs).map((input, index) => ({
        index,
        type: input.type || 'textarea',
        name: input.name,
        id: input.id,
        required: input.required,
        disabled: input.disabled,
        readonly: input.readOnly
      }));
      
      return { forms: formData, inputs: inputData };
    });
    
    console.log(`表单元素信息 (${browserName}):`, formElements);
    
    // 测试不同类型的表单控件
    const inputTypes = ['text', 'email', 'password', 'number', 'tel', 'url', 'search', 'date', 'time'];
    
    for (const inputType of inputTypes) {
      const input = page.locator(`input[type="${inputType}"]`).first();
      
      if (await input.count() > 0) {
        try {
          await input.focus();
          await input.fill('test value');
          await input.blur();
          
          const value = await input.inputValue();
          console.log(`${inputType} 输入测试 (${browserName}): ${value ? '成功' : '失败'}`);
        } catch (error) {
          console.log(`${inputType} 输入测试失败 (${browserName}): ${error.message}`);
        }
      }
    }
    
    // 测试选择框
    const selectElements = await page.locator('select').all();
    for (let i = 0; i < selectElements.length; i++) {
      const select = selectElements[i];
      
      try {
        const options = await select.locator('option').all();
        if (options.length > 1) {
          await select.selectOption({ index: 1 });
          const value = await select.inputValue();
          console.log(`选择框测试 ${i} (${browserName}): ${value ? '成功' : '失败'}`);
        }
      } catch (error) {
        console.log(`选择框测试 ${i} 失败 (${browserName}): ${error.message}`);
      }
    }
    
    // 测试复选框和单选按钮
    const checkboxes = await page.locator('input[type="checkbox"]').all();
    for (let i = 0; i < Math.min(checkboxes.length, 3); i++) {
      const checkbox = checkboxes[i];
      try {
        await checkbox.check();
        const isChecked = await checkbox.isChecked();
        console.log(`复选框测试 ${i} (${browserName}): ${isChecked ? '成功' : '失败'}`);
      } catch (error) {
        console.log(`复选框测试 ${i} 失败 (${browserName}): ${error.message}`);
      }
    }
    
    const radios = await page.locator('input[type="radio"]').all();
    for (let i = 0; i < Math.min(radios.length, 3); i++) {
      const radio = radios[i];
      try {
        await radio.check();
        const isChecked = await radio.isChecked();
        console.log(`单选按钮测试 ${i} (${browserName}): ${isChecked ? '成功' : '失败'}`);
      } catch (error) {
        console.log(`单选按钮测试 ${i} 失败 (${browserName}): ${error.message}`);
      }
    }
  });
  
  // 文件上传兼容性测试
  test('文件上传兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const fileInputs = await page.locator('input[type="file"]').all();
    
    if (fileInputs.length > 0) {
      const fileInput = fileInputs[0];
      
      // 检查文件输入支持的属性
      const fileInputInfo = await page.evaluate(() => {
        const input = document.querySelector('input[type="file"]');
        if (!input) return null;
        
        return {
          multiple: input.hasAttribute('multiple'),
          accept: input.getAttribute('accept'),
          capture: input.getAttribute('capture'),
          webkitdirectory: input.hasAttribute('webkitdirectory'),
          // 检查 File API 支持
          fileAPISupport: {
            File: typeof File !== 'undefined',
            FileList: typeof FileList !== 'undefined',
            FileReader: typeof FileReader !== 'undefined',
            Blob: typeof Blob !== 'undefined'
          }
        };
      });
      
      console.log(`文件上传功能 (${browserName}):`, fileInputInfo);
      
      // 测试基本的文件选择（使用测试文件）
      try {
        // 创建一个测试文件
        await page.evaluate(() => {
          const testFile = new Blob(['test content'], { type: 'text/plain' });
          const input = document.querySelector('input[type="file"]');
          
          // 模拟文件选择（这在真实浏览器中需要用户交互）
          const dataTransfer = new DataTransfer();
          const file = new File([testFile], 'test.txt', { type: 'text/plain' });
          dataTransfer.items.add(file);
          
          if (input) {
            input.files = dataTransfer.files;
            input.dispatchEvent(new Event('change', { bubbles: true }));
          }
        });
        
        console.log(`文件选择模拟完成 (${browserName})`);
      } catch (error) {
        console.log(`文件选择测试失败 (${browserName}): ${error.message}`);
      }
    }
  });
  
  // 拖拽功能兼容性测试
  test('拖拽功能兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 检查拖拽API支持
    const dragDropSupport = await page.evaluate(() => {
      return {
        dragStart: typeof DragEvent !== 'undefined',
        dataTransfer: typeof DataTransfer !== 'undefined',
        dragAndDropAPI: 'draggable' in document.createElement('div'),
        // 检查 HTML5 拖拽事件
        dragEvents: [
          'dragstart', 'drag', 'dragenter', 'dragover', 'dragleave', 'drop', 'dragend'
        ].every(event => `on${event}` in document.createElement('div'))
      };
    });
    
    console.log(`拖拽功能支持 (${browserName}):`, dragDropSupport);
    
    // 查找可拖拽元素
    const draggableElements = await page.locator('[draggable="true"], [draggable]').all();
    
    if (draggableElements.length > 0) {
      const draggable = draggableElements[0];
      
      try {
        // 获取元素位置
        const boundingBox = await draggable.boundingBox();
        
        if (boundingBox) {
          // 模拟拖拽事件
          await page.evaluate((box) => {
            const element = document.elementFromPoint(box.x + box.width/2, box.y + box.height/2);
            if (element) {
              const dragEvent = new DragEvent('dragstart', {
                bubbles: true,
                cancelable: true,
                dataTransfer: new DataTransfer()
              });
              element.dispatchEvent(dragEvent);
            }
          }, boundingBox);
          
          console.log(`拖拽事件模拟完成 (${browserName})`);
        }
      } catch (error) {
        console.log(`拖拽测试失败 (${browserName}): ${error.message}`);
      }
    }
  });
  
  // 复制粘贴功能兼容性测试
  test('复制粘贴功能兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 检查剪贴板API支持
    const clipboardSupport = await page.evaluate(() => {
      return {
        clipboardAPI: typeof navigator.clipboard !== 'undefined',
        execCommand: typeof document.execCommand === 'function',
        readText: typeof navigator.clipboard?.readText === 'function',
        writeText: typeof navigator.clipboard?.writeText === 'function',
        read: typeof navigator.clipboard?.read === 'function',
        write: typeof navigator.clipboard?.write === 'function'
      };
    });
    
    console.log(`剪贴板功能支持 (${browserName}):`, clipboardSupport);
    
    // 测试文本选择和复制
    const textElements = await page.locator('p, h1, h2, h3, span').all();
    
    if (textElements.length > 0) {
      const textElement = textElements[0];
      
      try {
        // 选择文本
        await textElement.dblclick();
        
        // 尝试复制
        await page.keyboard.press('Control+c');
        
        // 查找输入框进行粘贴测试
        const inputs = await page.locator('input[type="text"], textarea').all();
        if (inputs.length > 0) {
          await inputs[0].focus();
          await page.keyboard.press('Control+v');
          
          console.log(`复制粘贴测试完成 (${browserName})`);
        }
      } catch (error) {
        console.log(`复制粘贴测试失败 (${browserName}): ${error.message}`);
      }
    }
  });
  
  // 页面滚动兼容性测试
  test('页面滚动兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 检查滚动API支持
    const scrollSupport = await page.evaluate(() => {
      return {
        scrollTo: typeof window.scrollTo === 'function',
        scrollBy: typeof window.scrollBy === 'function',
        scrollIntoView: typeof Element.prototype.scrollIntoView === 'function',
        smoothScroll: CSS.supports('scroll-behavior', 'smooth'),
        scrollSnapType: CSS.supports('scroll-snap-type', 'x mandatory')
      };
    });
    
    console.log(`滚动功能支持 (${browserName}):`, scrollSupport);
    
    // 测试不同的滚动方式
    const scrollTests = [
      { method: 'scrollTo', params: [0, 100] },
      { method: 'scrollBy', params: [0, 50] },
      { method: 'scrollTo', params: [0, 0] }
    ];
    
    for (const test of scrollTests) {
      try {
        await page.evaluate((testData) => {
          window[testData.method](...testData.params);
        }, test);
        
        await page.waitForTimeout(500);
        
        const scrollPosition = await page.evaluate(() => ({
          x: window.pageXOffset || window.scrollX,
          y: window.pageYOffset || window.scrollY
        }));
        
        console.log(`${test.method} 滚动测试 (${browserName}): x=${scrollPosition.x}, y=${scrollPosition.y}`);
      } catch (error) {
        console.log(`${test.method} 滚动测试失败 (${browserName}): ${error.message}`);
      }
    }
    
    // 测试元素滚动到视口
    const scrollableElements = await page.locator('*').all();
    if (scrollableElements.length > 5) {
      try {
        await scrollableElements[5].scrollIntoViewIfNeeded();
        console.log(`scrollIntoView 测试完成 (${browserName})`);
      } catch (error) {
        console.log(`scrollIntoView 测试失败 (${browserName}): ${error.message}`);
      }
    }
  });
});