import { test, expect } from '@playwright/test';

/**
 * 验证按需加载时不显示过场动画的测试
 */
test.describe('按需加载无过场动画测试', () => {

  test('点击"启动智能对话"后不应显示PixelLoader动画', async ({ page, browserName }) => {
    console.log(`\n💬 测试智能对话按需加载无动画 (${browserName})`);
    
    await page.goto('/');
    
    // 等待页面加载完成
    await page.waitForSelector('nav', { timeout: 10000 });
    
    // 滚动到智能对话部分
    await page.locator('#chat').scrollIntoViewIfNeeded();
    
    // 查找启动按钮（区分登录状态）
    const chatStartButton = page.locator('text=启动智能对话').first();
    const loginButton = page.locator('#chat text=登录后使用').first();
    
    if (await loginButton.isVisible()) {
      console.log('用户未登录，跳过动画测试');
      return;
    }
    
    if (await chatStartButton.isVisible()) {
      console.log('用户已登录，测试按需加载无动画');
      
      // 点击启动智能对话按钮
      await chatStartButton.click();
      
      // 等待一段时间，确保如果有PixelLoader动画会显示出来
      await page.waitForTimeout(1000);
      
      // 检查是否存在PixelLoader相关的元素
      const pixelLoaderElements = await page.locator('.grid.gap-1, .pixel-animation, [class*="pixel"]').count();
      
      console.log(`找到的可能的PixelLoader元素数量: ${pixelLoaderElements}`);
      
      // 应该没有PixelLoader动画元素
      expect(pixelLoaderElements).toBeLessThanOrEqual(0);
      
      // 检查应该有简单的加载提示（进度条等）
      const simpleLoader = page.locator('text=正在加载智能对话, [class*="bg-gradient-to-r from-cyan-400"]').first();
      
      if (await simpleLoader.isVisible()) {
        console.log('✅ 显示简单的加载提示，没有复杂的PixelLoader动画');
      }
      
      // 等待LobeChat开始加载
      const chatContainer = page.locator('.chat-container, iframe').first();
      await expect(chatContainer).toBeVisible({ timeout: 10000 });
      
      console.log('✅ 智能对话组件开始加载，无PixelLoader动画');
    }
  });

  test('点击"启动Web IDE"后不应显示过场动画', async ({ page, browserName }) => {
    console.log(`\n🚀 测试Web IDE按需加载无动画 (${browserName})`);
    
    await page.goto('/');
    
    // 等待页面加载完成
    await page.waitForSelector('nav', { timeout: 10000 });
    
    // 滚动到Web IDE部分
    await page.locator('#webide').scrollIntoViewIfNeeded();
    
    // 查找启动按钮
    const webIDEStartButton = page.locator('text=启动 Web IDE').first();
    const loginButton = page.locator('#webide text=登录后使用').first();
    
    if (await loginButton.isVisible()) {
      console.log('用户未登录，跳过动画测试');
      return;
    }
    
    if (await webIDEStartButton.isVisible()) {
      console.log('用户已登录，测试Web IDE按需加载无动画');
      
      // 点击启动Web IDE按钮
      await webIDEStartButton.click();
      
      // 等待一段时间，确保如果有过场动画会显示出来
      await page.waitForTimeout(1000);
      
      // 检查是否存在PixelLoader相关的元素
      const pixelLoaderElements = await page.locator('.grid.gap-1, .pixel-animation, [class*="pixel"]').count();
      
      console.log(`找到的可能的PixelLoader元素数量: ${pixelLoaderElements}`);
      
      // 应该没有PixelLoader动画元素
      expect(pixelLoaderElements).toBeLessThanOrEqual(0);
      
      // 检查可能存在的简单加载动画（如旋转圆圈）
      const simpleSpinner = page.locator('.animate-spin, [class*="spinner"]').first();
      
      // 简单的旋转动画是允许的，但不应该有复杂的PixelLoader
      if (await simpleSpinner.isVisible()) {
        console.log('✅ 显示简单的旋转加载动画，没有复杂的PixelLoader');
      }
      
      // 等待Web IDE开始加载
      const ideContainer = page.locator('iframe, .webide-iframe').first();
      await expect(ideContainer).toBeVisible({ timeout: 10000 });
      
      console.log('✅ Web IDE组件开始加载，无复杂过场动画');
    }
  });

  test('验证页面初始加载时的PixelLoader仍正常工作', async ({ page, browserName }) => {
    console.log(`\n⚡ 验证页面初始加载动画 (${browserName})`);
    
    // 快速导航，以便捕捉到初始加载动画
    const response = page.goto('/');
    
    // 尝试检查是否有初始加载动画
    try {
      const initialLoader = page.locator('.fixed.inset-0.z-50, .grid.gap-1').first();
      const isVisible = await initialLoader.isVisible({ timeout: 2000 });
      
      if (isVisible) {
        console.log('✅ 页面初始加载时正确显示PixelLoader动画');
        
        // 等待加载完成
        await expect(initialLoader).not.toBeVisible({ timeout: 10000 });
        console.log('✅ 初始加载动画正常消失');
      } else {
        console.log('⚡ 页面加载太快，未捕捉到初始动画（这是好事）');
      }
    } catch (error) {
      console.log('⚡ 页面加载速度很快，这表明性能优化生效');
    }
    
    await response;
    
    // 验证页面最终正常加载
    await expect(page.locator('nav')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('#home h1')).toBeVisible({ timeout: 5000 });
    
    console.log('✅ 页面初始加载流程正常');
  });

  test('验证按需加载按钮的用户体验优化', async ({ page, browserName }) => {
    console.log(`\n🎯 测试按需加载用户体验 (${browserName})`);
    
    await page.goto('/');
    
    // 等待页面加载
    await page.waitForSelector('nav', { timeout: 10000 });
    
    // 检查Web IDE和智能对话的卡片是否正确显示
    await page.locator('#webide').scrollIntoViewIfNeeded();
    const webIDECard = page.locator('#webide .bg-gray-900\\/50').first();
    await expect(webIDECard).toBeVisible();
    
    await page.locator('#chat').scrollIntoViewIfNeeded();
    const chatCard = page.locator('#chat .bg-gray-900\\/50').first();
    await expect(chatCard).toBeVisible();
    
    // 验证按钮文案正确
    const webIDEButton = webIDECard.locator('button').first();
    const chatButton = chatCard.locator('button').first();
    
    const webIDEButtonText = await webIDEButton.textContent();
    const chatButtonText = await chatButton.textContent();
    
    console.log(`Web IDE按钮文案: "${webIDEButtonText}"`);
    console.log(`智能对话按钮文案: "${chatButtonText}"`);
    
    // 验证按钮文案包含正确的内容
    expect(webIDEButtonText).toMatch(/(启动.*Web.*IDE|登录后使用)/);
    expect(chatButtonText).toMatch(/(启动.*智能对话|登录后使用)/);
    
    // 验证图标存在
    const webIDEIcon = webIDECard.locator('svg').first();
    const chatIcon = chatCard.locator('svg').first();
    
    await expect(webIDEIcon).toBeVisible();
    await expect(chatIcon).toBeVisible();
    
    console.log('✅ 按需加载按钮设计和用户体验正确');
  });

});