import { test, expect } from '@playwright/test';

// 浏览器功能兼容性测试套件
test.describe('浏览器功能兼容性测试', () => {
  
  // JavaScript ES6+ 特性兼容性测试
  test('JavaScript ES6+ 特性兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const jsFeatures = await page.evaluate(() => {
      const features = {};
      
      // ES6 基础特性
      try {
        // Arrow functions
        const arrow = () => true;
        features.arrowFunctions = arrow();
        
        // Template literals
        const name = 'test';
        features.templateLiterals = `Hello ${name}` === 'Hello test';
        
        // Destructuring
        const [a, b] = [1, 2];
        features.destructuring = a === 1 && b === 2;
        
        // Default parameters
        function testDefault(x = 5) { return x; }
        features.defaultParams = testDefault() === 5;
        
        // Spread operator
        const arr = [1, 2, 3];
        const spread = [...arr];
        features.spreadOperator = spread.length === 3;
        
        // Classes
        class TestClass { constructor() { this.value = 'test'; } }
        const instance = new TestClass();
        features.classes = instance.value === 'test';
        
        // Let/Const
        let testLet = 1;
        const testConst = 2;
        features.letConst = testLet === 1 && testConst === 2;
      } catch (e) {
        features.es6BasicError = e.message;
      }
      
      // ES2017+ 特性
      try {
        // Async/Await
        features.asyncAwait = (async () => true)() instanceof Promise;
        
        // Object.entries
        features.objectEntries = typeof Object.entries === 'function';
        
        // Object.values
        features.objectValues = typeof Object.values === 'function';
      } catch (e) {
        features.es2017Error = e.message;
      }
      
      // ES2018+ 特性
      try {
        // Rest/Spread in objects
        const obj = { a: 1, b: 2, c: 3 };
        const { a, ...rest } = obj;
        features.objectRestSpread = rest.b === 2 && rest.c === 3;
        
        // Async iteration
        features.asyncIteration = typeof Symbol.asyncIterator !== 'undefined';
      } catch (e) {
        features.es2018Error = e.message;
      }
      
      // ES2019+ 特性
      try {
        // Array.flat
        features.arrayFlat = typeof Array.prototype.flat === 'function';
        
        // Array.flatMap
        features.arrayFlatMap = typeof Array.prototype.flatMap === 'function';
        
        // Object.fromEntries
        features.objectFromEntries = typeof Object.fromEntries === 'function';
        
        // Optional catch binding
        try {
          throw new Error('test');
        } catch {
          features.optionalCatchBinding = true;
        }
      } catch (e) {
        features.es2019Error = e.message;
      }
      
      // ES2020+ 特性
      try {
        // Nullish coalescing
        const test = null ?? 'default';
        features.nullishCoalescing = test === 'default';
        
        // Optional chaining
        const obj = { nested: { value: 'test' } };
        features.optionalChaining = obj?.nested?.value === 'test';
        
        // BigInt
        features.bigInt = typeof BigInt === 'function';
        
        // Dynamic import (check if supported indirectly)
        features.dynamicImport = 'import' in window || 
                                'importScripts' in window ||
                                Function.prototype.toString.call(() => {}).includes('[native code]');
      } catch (e) {
        features.es2020Error = e.message;
      }
      
      return features;
    });
    
    console.log(`JavaScript 特性支持 (${browserName}):`, jsFeatures);
    
    // 基础 ES6 特性应该在所有现代浏览器中支持
    expect(jsFeatures.arrowFunctions).toBe(true);
    expect(jsFeatures.templateLiterals).toBe(true);
    expect(jsFeatures.letConst).toBe(true);
    
    // 记录测试结果
    await page.addInitScript((features) => {
      window.testResults = window.testResults || {};
      window.testResults.jsFeatures = features;
    }, jsFeatures);
  });

  // CSS 特性兼容性测试
  test('CSS 特性兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const cssFeatures = await page.evaluate(() => {
      const features = {};
      const testElement = document.createElement('div');
      document.body.appendChild(testElement);
      
      try {
        // Flexbox 支持
        testElement.style.display = 'flex';
        features.flexbox = testElement.style.display === 'flex';
        
        // CSS Grid 支持
        testElement.style.display = 'grid';
        features.cssGrid = testElement.style.display === 'grid';
        
        // CSS Custom Properties (Variables)
        testElement.style.setProperty('--test-var', 'red');
        testElement.style.color = 'var(--test-var)';
        const computed = getComputedStyle(testElement);
        features.cssVariables = computed.color === 'red' || 
                                 computed.color === 'rgb(255, 0, 0)';
        
        // Transform 支持
        testElement.style.transform = 'translateX(10px)';
        features.transforms = testElement.style.transform === 'translateX(10px)';
        
        // Transition 支持
        testElement.style.transition = 'all 0.3s ease';
        features.transitions = testElement.style.transition.includes('0.3s');
        
        // Animation 支持
        testElement.style.animation = 'none';
        features.animations = testElement.style.animation === 'none';
        
        // Border-radius 支持
        testElement.style.borderRadius = '5px';
        features.borderRadius = testElement.style.borderRadius === '5px';
        
        // Box-shadow 支持
        testElement.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        features.boxShadow = testElement.style.boxShadow !== '';
        
        // Media queries 支持
        features.mediaQueries = typeof window.matchMedia === 'function';
        
        // CSS Object Model 支持
        features.cssOM = typeof CSS !== 'undefined';
        
        // Container Queries 支持 (新特性)
        features.containerQueries = CSS?.supports?.('container-type: inline-size') || false;
        
        // Scroll Snap 支持
        testElement.style.scrollSnapType = 'x mandatory';
        features.scrollSnap = testElement.style.scrollSnapType !== '';
        
        // CSS Clamp 支持
        features.cssClamp = CSS?.supports?.('width: clamp(1rem, 2.5vw, 2rem)') || false;
        
        // CSS Grid Subgrid 支持
        features.subgrid = CSS?.supports?.('grid-template-columns: subgrid') || false;
        
      } catch (e) {
        features.cssError = e.message;
      } finally {
        document.body.removeChild(testElement);
      }
      
      return features;
    });
    
    console.log(`CSS 特性支持 (${browserName}):`, cssFeatures);
    
    // 基础 CSS 特性应该支持
    expect(cssFeatures.flexbox).toBe(true);
    expect(cssFeatures.transforms).toBe(true);
    expect(cssFeatures.transitions).toBe(true);
    expect(cssFeatures.borderRadius).toBe(true);
    
    // 记录测试结果
    await page.addInitScript((features) => {
      window.testResults = window.testResults || {};
      window.testResults.cssFeatures = features;
    }, cssFeatures);
  });

  // Web API 兼容性测试
  test('Web API 兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const apiSupport = await page.evaluate(() => {
      const apis = {};
      
      // 存储 APIs
      apis.localStorage = typeof localStorage !== 'undefined';
      apis.sessionStorage = typeof sessionStorage !== 'undefined';
      apis.indexedDB = typeof indexedDB !== 'undefined';
      
      // 网络 APIs
      apis.fetch = typeof fetch === 'function';
      apis.xmlHttpRequest = typeof XMLHttpRequest === 'function';
      apis.webSocket = typeof WebSocket === 'function';
      
      // DOM APIs
      apis.querySelector = typeof document.querySelector === 'function';
      apis.addEventListener = typeof document.addEventListener === 'function';
      apis.getBoundingClientRect = typeof Element.prototype.getBoundingClientRect === 'function';
      
      // 现代 Observer APIs
      apis.mutationObserver = typeof MutationObserver === 'function';
      apis.intersectionObserver = typeof IntersectionObserver === 'function';
      apis.resizeObserver = typeof ResizeObserver === 'function';
      apis.performanceObserver = typeof PerformanceObserver === 'function';
      
      // 媒体 APIs
      apis.mediaQueries = typeof window.matchMedia === 'function';
      apis.audioContext = typeof AudioContext === 'function' || 
                          typeof window.webkitAudioContext === 'function';
      apis.getUserMedia = typeof navigator.mediaDevices?.getUserMedia === 'function';
      
      // 设备 APIs
      apis.geolocation = typeof navigator.geolocation === 'object';
      apis.deviceOrientation = 'DeviceOrientationEvent' in window;
      apis.accelerometer = 'DeviceMotionEvent' in window;
      apis.vibration = typeof navigator.vibrate === 'function';
      
      // 新兴 APIs
      apis.serviceWorker = 'serviceWorker' in navigator;
      apis.webWorker = typeof Worker === 'function';
      apis.pushNotifications = 'Notification' in window;
      apis.paymentRequest = typeof PaymentRequest === 'function';
      apis.webShare = typeof navigator.share === 'function';
      apis.webAuthn = typeof navigator.credentials?.create === 'function';
      
      // Canvas/WebGL APIs
      apis.canvas2d = (() => {
        try {
          const canvas = document.createElement('canvas');
          return !!canvas.getContext('2d');
        } catch (e) { return false; }
      })();
      
      apis.webgl = (() => {
        try {
          const canvas = document.createElement('canvas');
          return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
        } catch (e) { return false; }
      })();
      
      apis.webgl2 = (() => {
        try {
          const canvas = document.createElement('canvas');
          return !!canvas.getContext('webgl2');
        } catch (e) { return false; }
      })();
      
      // Performance APIs
      apis.performance = typeof performance === 'object';
      apis.performanceTiming = typeof performance.timing === 'object';
      apis.performanceNavigation = typeof performance.navigation === 'object';
      apis.performanceMeasure = typeof performance.measure === 'function';
      
      // CSS APIs
      apis.cssSupports = typeof CSS?.supports === 'function';
      apis.getComputedStyle = typeof getComputedStyle === 'function';
      
      return apis;
    });
    
    console.log(`Web API 支持 (${browserName}):`, apiSupport);
    
    // 基础 APIs 应该支持
    expect(apiSupport.localStorage).toBe(true);
    expect(apiSupport.fetch).toBe(true);
    expect(apiSupport.querySelector).toBe(true);
    expect(apiSupport.addEventListener).toBe(true);
    
    // 记录测试结果
    await page.addInitScript((apis) => {
      window.testResults = window.testResults || {};
      window.testResults.webAPIs = apis;
    }, apiSupport);
  });

  // 性能相关特性测试
  test('性能相关特性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const performanceFeatures = await page.evaluate(() => {
      const perf = {};
      
      // Performance API 基础支持
      perf.performanceNow = typeof performance.now === 'function';
      perf.performanceMark = typeof performance.mark === 'function';
      perf.performanceMeasure = typeof performance.measure === 'function';
      
      // Resource Timing API
      perf.resourceTiming = typeof performance.getEntriesByType === 'function';
      
      // Navigation Timing API
      perf.navigationTiming = typeof performance.timing === 'object';
      
      // User Timing API
      perf.userTiming = typeof performance.mark === 'function' &&
                        typeof performance.measure === 'function';
      
      // Paint Timing API
      try {
        const paintEntries = performance.getEntriesByType('paint');
        perf.paintTiming = Array.isArray(paintEntries);
      } catch (e) {
        perf.paintTiming = false;
      }
      
      // Long Task API
      try {
        const longTasks = performance.getEntriesByType('longtask');
        perf.longTaskAPI = Array.isArray(longTasks);
      } catch (e) {
        perf.longTaskAPI = false;
      }
      
      // Memory API (仅 Chrome)
      perf.memoryAPI = typeof performance.memory === 'object';
      
      // High Resolution Time
      perf.highResTime = performance.timeOrigin !== undefined;
      
      return perf;
    });
    
    console.log(`性能特性支持 (${browserName}):`, performanceFeatures);
    
    // 基础性能 API 应该支持
    expect(performanceFeatures.performanceNow).toBe(true);
    
    // 记录测试结果
    await page.addInitScript((perf) => {
      window.testResults = window.testResults || {};
      window.testResults.performanceFeatures = perf;
    }, performanceFeatures);
  });

  // 安全相关特性测试
  test('安全相关特性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const securityFeatures = await page.evaluate(() => {
      const security = {};
      
      // HTTPS 检测
      security.isHttps = location.protocol === 'https:';
      
      // CSP 支持
      security.csp = typeof CSP !== 'undefined' || 
                     document.querySelector('meta[http-equiv="Content-Security-Policy"]') !== null;
      
      // Referrer Policy 支持
      security.referrerPolicy = 'referrerPolicy' in document;
      
      // SameSite Cookie 支持（通过设置测试）
      try {
        document.cookie = 'test=1; SameSite=Strict';
        security.sameSiteCookies = document.cookie.includes('test=1');
        // 清理测试 cookie
        document.cookie = 'test=; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      } catch (e) {
        security.sameSiteCookies = false;
      }
      
      // Secure Context 检测
      security.secureContext = typeof isSecureContext !== 'undefined' ? isSecureContext : false;
      
      // SubResource Integrity 支持
      security.sri = 'integrity' in document.createElement('script');
      
      // Feature Policy/Permissions Policy 支持
      security.featurePolicy = typeof document.featurePolicy === 'object' ||
                               typeof document.permissionsPolicy === 'object';
      
      // Cross Origin Isolation
      security.crossOriginIsolated = typeof crossOriginIsolated !== 'undefined' ? 
                                     crossOriginIsolated : false;
      
      return security;
    });
    
    console.log(`安全特性支持 (${browserName}):`, securityFeatures);
    
    // 记录测试结果
    await page.addInitScript((security) => {
      window.testResults = window.testResults || {};
      window.testResults.securityFeatures = security;
    }, securityFeatures);
  });

  // 最终兼容性报告生成
  test('生成兼容性报告', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 等待其他测试完成
    await page.waitForTimeout(1000);
    
    const fullReport = await page.evaluate((browser) => {
      const results = window.testResults || {};
      
      // 计算支持程度
      const calculateSupport = (features) => {
        if (!features || typeof features !== 'object') return 0;
        const entries = Object.entries(features);
        const total = entries.length;
        const supported = entries.filter(([key, value]) => 
          value === true && !key.includes('Error')
        ).length;
        return total > 0 ? Math.round((supported / total) * 100) : 0;
      };
      
      const report = {
        browser: browser,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        support: {
          javascript: calculateSupport(results.jsFeatures || {}),
          css: calculateSupport(results.cssFeatures || {}),
          webAPIs: calculateSupport(results.webAPIs || {}),
          performance: calculateSupport(results.performanceFeatures || {}),
          security: calculateSupport(results.securityFeatures || {})
        },
        details: results
      };
      
      return report;
    }, browserName);
    
    console.log(`\n===== 兼容性测试报告 (${browserName}) =====`);
    console.log(`用户代理: ${fullReport.userAgent}`);
    console.log(`视口大小: ${fullReport.viewport.width}x${fullReport.viewport.height}`);
    console.log(`JavaScript 支持度: ${fullReport.support.javascript}%`);
    console.log(`CSS 支持度: ${fullReport.support.css}%`);
    console.log(`Web APIs 支持度: ${fullReport.support.webAPIs}%`);
    console.log(`性能特性支持度: ${fullReport.support.performance}%`);
    console.log(`安全特性支持度: ${fullReport.support.security}%`);
    console.log(`=====================================\n`);
    
    // 验证基本兼容性要求
    expect(fullReport.support.javascript).toBeGreaterThan(70);
    expect(fullReport.support.css).toBeGreaterThan(70);
    expect(fullReport.support.webAPIs).toBeGreaterThan(60);
  });
});