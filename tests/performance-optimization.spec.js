import { test, expect } from '@playwright/test';

// 深入的性能优化测试
test.describe('低配设备性能优化测试', () => {

  // 模拟不同性能配置的设备测试
  test('超低性能设备模拟测试', async ({ page, browserName }) => {
    console.log(`\n🐌 超低性能设备测试 (${browserName})`);
    
    // 获取CDP会话用于性能控制
    const client = await page.context().newCDPSession(page);
    
    // 模拟极低性能设备
    await client.send('Emulation.setCPUThrottlingRate', { rate: 6 }); // 6倍CPU节流
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: 200 * 1024, // 200KB/s (2G网络)
      uploadThroughput: 100 * 1024,
      latency: 800 // 800ms延迟
    });
    
    // 监控性能指标
    const performanceMetrics = [];
    let loadStartTime = Date.now();
    
    // 监听长任务 (>50ms)
    await page.addInitScript(() => {
      window.longTasks = [];
      if (window.PerformanceObserver) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            window.longTasks.push({
              duration: entry.duration,
              startTime: entry.startTime
            });
          }
        });
        observer.observe({entryTypes: ['longtask']});
      }
    });
    
    console.log('开始极低性能环境加载...');
    await page.goto('/');
    
    // 监控首次内容绘制时间
    const firstContentfulPaint = await page.evaluate(() => {
      return new Promise(resolve => {
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              resolve(entry.startTime);
            }
          }
        }).observe({entryTypes: ['paint']});
        
        // 如果5秒内没有FCP，返回超时
        setTimeout(() => resolve(null), 5000);
      });
    });
    
    // 等待页面基本可用（可能需要很长时间）
    try {
      await page.waitForFunction(() => {
        const root = document.querySelector('#root');
        return root && root.children.length > 0;
      }, { timeout: 30000 });
      
      console.log('✅ 页面基本内容加载完成');
    } catch (error) {
      console.warn('⚠️ 页面加载超时，但这在极低性能环境下可能是正常的');
    }
    
    const totalLoadTime = Date.now() - loadStartTime;
    console.log(`极低性能设备总加载时间: ${totalLoadTime}ms`);
    
    // 检查长任务
    const longTasks = await page.evaluate(() => window.longTasks || []);
    console.log(`检测到长任务数量: ${longTasks.length}`);
    
    longTasks.forEach((task, index) => {
      if (task.duration > 100) { // 超过100ms的任务
        console.warn(`长任务${index + 1}: ${task.duration.toFixed(0)}ms`);
      }
    });
    
    // 在极低性能环境下，允许更长的加载时间
    expect(totalLoadTime).toBeLessThan(40000); // 40秒内
    
    // 恢复正常性能
    await client.send('Emulation.setCPUThrottlingRate', { rate: 1 });
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: -1,
      uploadThroughput: -1,
      latency: 0
    });
    
    console.log(`✅ 超低性能设备测试完成 (${browserName})`);
  });

  // JavaScript执行性能测试
  test('JavaScript执行性能分析', async ({ page, browserName }) => {
    console.log(`\n⚡ JavaScript执行性能测试 (${browserName})`);
    
    await page.goto('/');
    
    // 测试大量DOM操作的性能
    const domPerformance = await page.evaluate(() => {
      const results = {};
      
      // 测试DOM查询性能
      console.time('DOM查询测试');
      for (let i = 0; i < 1000; i++) {
        document.querySelector('body');
        document.querySelectorAll('div');
      }
      console.timeEnd('DOM查询测试');
      
      // 测试数组操作性能
      console.time('数组操作测试');
      const largeArray = Array.from({length: 10000}, (_, i) => i);
      const filtered = largeArray.filter(n => n % 2 === 0);
      const mapped = filtered.map(n => n * 2);
      console.timeEnd('数组操作测试');
      
      // 测试对象创建性能
      console.time('对象创建测试');
      const objects = [];
      for (let i = 0; i < 1000; i++) {
        objects.push({
          id: i,
          name: `item-${i}`,
          data: { value: i * 10 }
        });
      }
      console.timeEnd('对象创建测试');
      
      return {
        arrayLength: mapped.length,
        objectsLength: objects.length
      };
    });
    
    console.log('DOM性能测试结果:', domPerformance);
    
    // 测试React组件渲染性能
    const reactPerformance = await page.evaluate(() => {
      // 检查React DevTools性能信息（如果可用）
      if (window.React && window.React.Profiler) {
        return { hasProfiler: true };
      }
      return { hasProfiler: false };
    });
    
    console.log('React性能检测:', reactPerformance);
    
    console.log(`✅ JavaScript性能测试完成 (${browserName})`);
  });

  // CSS渲染性能测试
  test('CSS渲染和重排性能测试', async ({ page, browserName }) => {
    console.log(`\n🎨 CSS渲染性能测试 (${browserName})`);
    
    await page.goto('/');
    
    // 等待基本样式加载
    await page.waitForLoadState('domcontentloaded');
    
    // 测试强制重排的性能影响
    const layoutPerformance = await page.evaluate(() => {
      const results = {};
      const testElement = document.createElement('div');
      testElement.style.position = 'absolute';
      testElement.style.top = '0px';
      testElement.style.left = '0px';
      document.body.appendChild(testElement);
      
      // 测试大量样式更改的性能
      const startTime = performance.now();
      
      for (let i = 0; i < 100; i++) {
        testElement.style.left = `${i}px`;
        testElement.offsetLeft; // 强制重排
      }
      
      const endTime = performance.now();
      results.layoutThrashingTime = endTime - startTime;
      
      // 清理测试元素
      document.body.removeChild(testElement);
      
      // 检查当前页面的CSS复杂度
      const allElements = document.querySelectorAll('*');
      results.elementCount = allElements.length;
      
      // 统计复杂选择器的使用
      let complexSelectors = 0;
      allElements.forEach(el => {
        const styles = window.getComputedStyle(el);
        if (styles.boxShadow !== 'none') complexSelectors++;
        if (styles.background.includes('gradient')) complexSelectors++;
        if (styles.transform !== 'none') complexSelectors++;
      });
      
      results.complexSelectorsCount = complexSelectors;
      
      return results;
    });
    
    console.log('CSS渲染性能:', {
      layoutThrashingTime: `${layoutPerformance.layoutThrashingTime.toFixed(2)}ms`,
      elementCount: layoutPerformance.elementCount,
      complexSelectors: layoutPerformance.complexSelectorsCount
    });
    
    // 布局抖动时间应该合理
    expect(layoutPerformance.layoutThrashingTime).toBeLessThan(500);
    
    console.log(`✅ CSS渲染性能测试完成 (${browserName})`);
  });

  // 内存使用和垃圾回收测试
  test('内存使用优化测试', async ({ page, browserName }) => {
    console.log(`\n💾 内存使用优化测试 (${browserName})`);
    
    // 获取初始内存
    const initialMemory = await page.evaluate(() => {
      if (performance.memory) {
        return {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        };
      }
      return null;
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 模拟用户交互增加内存使用
    await page.evaluate(() => {
      // 创建一些对象模拟内存使用
      window.testObjects = [];
      for (let i = 0; i < 1000; i++) {
        window.testObjects.push({
          id: i,
          data: new Array(100).fill(i),
          element: document.createElement('div')
        });
      }
    });
    
    // 检查内存增长
    const afterCreationMemory = await page.evaluate(() => {
      if (performance.memory) {
        return {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        };
      }
      return null;
    });
    
    // 清理对象
    await page.evaluate(() => {
      window.testObjects = null;
      if (window.gc) window.gc(); // 手动垃圾回收（如果可用）
    });
    
    await page.waitForTimeout(1000); // 等待垃圾回收
    
    const afterCleanupMemory = await page.evaluate(() => {
      if (performance.memory) {
        return {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        };
      }
      return null;
    });
    
    if (initialMemory && afterCreationMemory && afterCleanupMemory) {
      const creationIncrease = afterCreationMemory.used - initialMemory.used;
      const cleanupDecrease = afterCreationMemory.used - afterCleanupMemory.used;
      
      console.log('内存使用情况:');
      console.log(`  初始: ${(initialMemory.used / 1024 / 1024).toFixed(1)}MB`);
      console.log(`  创建对象后: ${(afterCreationMemory.used / 1024 / 1024).toFixed(1)}MB (+${(creationIncrease / 1024 / 1024).toFixed(1)}MB)`);
      console.log(`  清理后: ${(afterCleanupMemory.used / 1024 / 1024).toFixed(1)}MB (-${(cleanupDecrease / 1024 / 1024).toFixed(1)}MB)`);
      
      // 垃圾回收效率检查
      const gcEfficiency = cleanupDecrease / creationIncrease;
      console.log(`  垃圾回收效率: ${(gcEfficiency * 100).toFixed(1)}%`);
      
      // 内存泄漏检测
      expect(gcEfficiency).toBeGreaterThan(0.5); // 至少回收50%
    }
    
    console.log(`✅ 内存优化测试完成 (${browserName})`);
  });

  // 网络资源加载优化测试
  test('网络资源加载性能测试', async ({ page, browserName }) => {
    console.log(`\n🌐 网络资源加载测试 (${browserName})`);
    
    const resourceMetrics = {
      totalRequests: 0,
      totalSize: 0,
      cacheHits: 0,
      largeResources: [],
      slowRequests: []
    };
    
    // 监听所有网络请求
    page.on('response', response => {
      const url = response.url();
      const size = response.headers()['content-length'];
      
      resourceMetrics.totalRequests++;
      
      if (size) {
        const sizeNum = parseInt(size);
        resourceMetrics.totalSize += sizeNum;
        
        // 记录大文件
        if (sizeNum > 100000) { // 100KB以上
          resourceMetrics.largeResources.push({
            url: url.split('/').pop(),
            size: sizeNum
          });
        }
      }
      
      // 检查缓存
      if (response.status() === 304 || response.headers()['cache-control']) {
        resourceMetrics.cacheHits++;
      }
    });
    
    const loadStartTime = Date.now();
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    const loadEndTime = Date.now();
    
    console.log('网络资源统计:');
    console.log(`  总请求数: ${resourceMetrics.totalRequests}`);
    console.log(`  总大小: ${(resourceMetrics.totalSize / 1024 / 1024).toFixed(2)}MB`);
    console.log(`  缓存命中: ${resourceMetrics.cacheHits}`);
    console.log(`  加载时间: ${loadEndTime - loadStartTime}ms`);
    
    // 显示大文件
    if (resourceMetrics.largeResources.length > 0) {
      console.log('大文件资源:');
      resourceMetrics.largeResources.forEach(resource => {
        console.log(`  ${resource.url}: ${(resource.size / 1024).toFixed(0)}KB`);
      });
    }
    
    // 资源数量不应该过多
    expect(resourceMetrics.totalRequests).toBeLessThan(50);
    // 总大小不应该超过5MB
    expect(resourceMetrics.totalSize).toBeLessThan(5 * 1024 * 1024);
    
    console.log(`✅ 网络资源测试完成 (${browserName})`);
  });

  // 用户交互响应性测试
  test('用户交互响应性能测试', async ({ page, browserName }) => {
    console.log(`\n👆 用户交互响应测试 (${browserName})`);
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 测试按钮点击响应时间
    const clickElements = await page.locator('button, a[href], [role="button"]').all();
    
    if (clickElements.length > 0) {
      const testElement = clickElements[0];
      
      // 测试多次点击的响应时间
      const clickTimes = [];
      for (let i = 0; i < 5; i++) {
        const startTime = Date.now();
        
        await testElement.click();
        
        // 等待可能的DOM更新
        await page.waitForTimeout(50);
        
        const endTime = Date.now();
        clickTimes.push(endTime - startTime);
      }
      
      const avgClickTime = clickTimes.reduce((a, b) => a + b) / clickTimes.length;
      console.log(`平均点击响应时间: ${avgClickTime.toFixed(1)}ms`);
      
      // 点击响应应该很快
      expect(avgClickTime).toBeLessThan(100);
    }
    
    // 测试滚动性能
    const scrollPerformance = await page.evaluate(() => {
      return new Promise(resolve => {
        let frameCount = 0;
        let totalTime = 0;
        const startTime = performance.now();
        
        const measureFrame = () => {
          frameCount++;
          const currentTime = performance.now();
          totalTime = currentTime - startTime;
          
          if (frameCount < 60) { // 测试60帧
            window.scrollBy(0, 5);
            requestAnimationFrame(measureFrame);
          } else {
            resolve({
              frameCount,
              totalTime,
              fps: frameCount / (totalTime / 1000)
            });
          }
        };
        
        requestAnimationFrame(measureFrame);
      });
    });
    
    console.log(`滚动性能: ${scrollPerformance.fps.toFixed(1)} FPS`);
    
    // 滚动应该保持合理的帧率
    expect(scrollPerformance.fps).toBeGreaterThan(20);
    
    console.log(`✅ 交互响应测试完成 (${browserName})`);
  });

});