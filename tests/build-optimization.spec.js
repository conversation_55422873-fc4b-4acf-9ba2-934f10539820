import { test, expect } from '@playwright/test';
import { readFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

/**
 * 构建优化验证测试
 */
test.describe('构建优化验证', () => {

  test('验证构建文件大小优化', async () => {
    console.log('\n📊 分析构建输出文件大小...');
    
    const distDir = join(process.cwd(), 'dist/assets');
    
    try {
      const files = readdirSync(distDir);
      const jsFiles = files.filter(f => f.endsWith('.js'));
      const cssFiles = files.filter(f => f.endsWith('.css'));
      
      console.log(`发现 ${jsFiles.length} 个 JS 文件，${cssFiles.length} 个 CSS 文件`);
      
      // 分析 JS 文件大小
      const jsFileSizes = jsFiles.map(file => {
        const filePath = join(distDir, file);
        const stats = statSync(filePath);
        const sizeKB = (stats.size / 1024).toFixed(2);
        return { file, sizeKB: parseFloat(sizeKB) };
      }).sort((a, b) => b.sizeKB - a.sizeKB);
      
      console.log('\n📦 JS 文件大小排序:');
      jsFileSizes.forEach(({ file, sizeKB }) => {
        const status = sizeKB > 300 ? '🚨' : sizeKB > 150 ? '⚠️' : '✅';
        console.log(`  ${status} ${file}: ${sizeKB} KB`);
      });
      
      // 验证优化目标
      const largestJS = jsFileSizes[0];
      console.log(`\n🎯 最大 JS 文件: ${largestJS.file} (${largestJS.sizeKB} KB)`);
      
      // 优化目标：最大文件不超过 300KB
      expect(largestJS.sizeKB).toBeLessThan(300);
      
      // 验证文件数量增加（更好的分割）
      expect(jsFiles.length).toBeGreaterThan(15);
      
      // 分析 CSS 文件
      const cssFileSizes = cssFiles.map(file => {
        const filePath = join(distDir, file);
        const stats = statSync(filePath);
        const sizeKB = (stats.size / 1024).toFixed(2);
        return { file, sizeKB: parseFloat(sizeKB) };
      });
      
      console.log('\n🎨 CSS 文件大小:');
      cssFileSizes.forEach(({ file, sizeKB }) => {
        console.log(`  📄 ${file}: ${sizeKB} KB`);
      });
      
      console.log('\n✅ 构建文件大小优化验证完成');
      
    } catch (error) {
      console.error('无法读取构建目录:', error);
      // 如果构建目录不存在，跳过测试
      test.skip();
    }
  });

  test('验证代码分割效果', async () => {
    console.log('\n🔄 验证代码分割策略...');
    
    const distDir = join(process.cwd(), 'dist/assets');
    
    try {
      const files = readdirSync(distDir);
      const jsFiles = files.filter(f => f.endsWith('.js'));
      
      // 检查关键vendor包的分割
      const vendorPackages = {
        'react-dom': jsFiles.filter(f => f.includes('vendor-react-dom')),
        'react': jsFiles.filter(f => f.includes('vendor-react') && !f.includes('react-dom')),
        'icons': jsFiles.filter(f => f.includes('vendor-icons')),
        'markdown': jsFiles.filter(f => f.includes('vendor-markdown')),
        'highlight': jsFiles.filter(f => f.includes('vendor-highlight')),
        'polyfills': jsFiles.filter(f => f.includes('vendor-polyfills')),
        'misc': jsFiles.filter(f => f.includes('vendor-misc'))
      };
      
      console.log('\n📦 Vendor 包分割情况:');
      Object.entries(vendorPackages).forEach(([name, files]) => {
        if (files.length > 0) {
          console.log(`  ✅ ${name}: ${files.length} 个文件`);
          files.forEach(f => console.log(`    📄 ${f}`));
        } else {
          console.log(`  ⚠️ ${name}: 未找到对应文件`);
        }
      });
      
      // 验证关键包存在
      expect(vendorPackages['react-dom'].length).toBeGreaterThan(0);
      expect(vendorPackages['react'].length).toBeGreaterThan(0);
      
      // 验证图标库被分割
      expect(vendorPackages['icons'].length).toBeGreaterThan(1);
      
      // 检查组件分割
      const componentFiles = jsFiles.filter(f => f.includes('component-'));
      console.log(`\n🧩 组件文件分割: ${componentFiles.length} 个组件包`);
      componentFiles.forEach(f => console.log(`  📦 ${f}`));
      
      // 验证组件独立分割
      expect(componentFiles.length).toBeGreaterThan(3);
      
      console.log('\n✅ 代码分割效果验证完成');
      
    } catch (error) {
      console.error('代码分割验证失败:', error);
      test.skip();
    }
  });

  test('验证构建产物的加载性能', async ({ page, browserName }) => {
    console.log(`\n⚡ 测试构建产物加载性能 (${browserName})`);
    
    // 监听网络请求
    const requests = [];
    const responses = [];
    
    page.on('request', request => {
      requests.push({
        url: request.url(),
        resourceType: request.resourceType(),
        size: 0
      });
    });
    
    page.on('response', response => {
      responses.push({
        url: response.url(),
        status: response.status(),
        size: response.headers()['content-length'] || 0
      });
    });
    
    // 访问页面
    const startTime = Date.now();
    await page.goto('/');
    
    // 等待页面关键内容加载
    await page.waitForSelector('nav', { timeout: 10000 });
    await page.waitForSelector('#home h1', { timeout: 5000 });
    const loadTime = Date.now() - startTime;
    
    console.log(`页面加载时间: ${loadTime}ms`);
    
    // 分析资源加载
    const jsRequests = requests.filter(r => r.resourceType === 'script');
    const cssRequests = requests.filter(r => r.resourceType === 'stylesheet');
    
    console.log(`JS 请求数量: ${jsRequests.length}`);
    console.log(`CSS 请求数量: ${cssRequests.length}`);
    console.log(`总请求数量: ${requests.length}`);
    
    // 分析响应状态
    const successfulResponses = responses.filter(r => r.status >= 200 && r.status < 400);
    const failedResponses = responses.filter(r => r.status >= 400);
    
    console.log(`成功响应: ${successfulResponses.length}`);
    console.log(`失败响应: ${failedResponses.length}`);
    
    if (failedResponses.length > 0) {
      console.log('失败的请求:');
      failedResponses.forEach(r => console.log(`  ❌ ${r.status} ${r.url}`));
    }
    
    // 性能验证
    expect(loadTime).toBeLessThan(3000); // 3秒内加载完成
    expect(failedResponses.length).toBeLessThan(5); // 允许少量失败请求（可能是外部资源）
    
    // 验证构建优化效果
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
        loadComplete: navigation.loadEventEnd - navigation.navigationStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
      };
    });
    
    console.log('\n📈 性能指标:');
    console.log(`  DOM加载完成: ${performanceMetrics.domContentLoaded.toFixed(2)}ms`);
    console.log(`  页面完全加载: ${performanceMetrics.loadComplete.toFixed(2)}ms`);
    console.log(`  首次绘制: ${performanceMetrics.firstPaint.toFixed(2)}ms`);
    console.log(`  首次内容绘制: ${performanceMetrics.firstContentfulPaint.toFixed(2)}ms`);
    
    // 验证核心性能指标
    expect(performanceMetrics.domContentLoaded).toBeLessThan(2000);
    expect(performanceMetrics.firstContentfulPaint).toBeLessThan(1500);
    
    console.log(`✅ 构建产物加载性能测试完成 (${browserName})`);
  });

  test('验证按需加载与构建优化的协调', async ({ page, browserName }) => {
    console.log(`\n🔄 测试按需加载与构建优化协调 (${browserName})`);
    
    await page.goto('/');
    
    // 等待页面基础加载
    await page.waitForSelector('nav', { timeout: 10000 });
    
    // 统计初始加载的资源
    const initialResources = await page.evaluate(() => {
      return performance.getEntriesByType('resource').length;
    });
    
    console.log(`初始资源数量: ${initialResources}`);
    
    // 滚动到Web IDE部分，验证按需加载按钮
    await page.locator('#webide').scrollIntoViewIfNeeded();
    const webIDEButton = page.locator('#webide button').first();
    await expect(webIDEButton).toBeVisible();
    
    // 滚动到智能对话部分
    await page.locator('#chat').scrollIntoViewIfNeeded();
    const chatButton = page.locator('#chat button').first();
    await expect(chatButton).toBeVisible();
    
    console.log('✅ 按需加载按钮正确显示');
    
    // 验证自动性能优化系统仍然工作
    await page.waitForFunction(() => {
      return window.autoPerformanceManager && window.autoPerformanceManager.isInitialized;
    }, { timeout: 10000 });
    
    const performanceStatus = await page.evaluate(() => {
      return window.autoPerformanceManager.getPerformanceStatus();
    });
    
    console.log(`性能优化级别: ${performanceStatus.currentLevel}`);
    console.log(`设备评分: ${performanceStatus.deviceCapabilities?.overallScore}`);
    
    // 验证构建优化没有影响运行时功能
    expect(performanceStatus.currentLevel).toBeTruthy();
    expect(performanceStatus.deviceCapabilities).toBeTruthy();
    
    console.log(`✅ 按需加载与构建优化协调测试完成 (${browserName})`);
  });

});