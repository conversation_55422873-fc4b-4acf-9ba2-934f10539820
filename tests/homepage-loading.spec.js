import { test, expect } from '@playwright/test';

// 主页完整加载流程测试
test.describe('主页完整加载流程测试', () => {
  
  // 完整的页面加载流程测试
  test('主页完整加载流程（从动画到内容显示）', async ({ page, browserName }) => {
    console.log(`\n🏠 测试主页完整加载流程 (${browserName})`);
    
    const loadingStages = {
      pageStart: false,
      animationStart: false,
      animationComplete: false,
      reactMount: false,
      componentsLoaded: false,
      interactionReady: false
    };
    
    // 监听各个阶段的完成情况
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('PixelLoader') && text.includes('开始启动')) {
        loadingStages.animationStart = true;
        console.log('📍 动画开始阶段');
      }
      if (text.includes('启动完成') || text.includes('加载已完成')) {
        loadingStages.animationComplete = true;
        console.log('📍 动画完成阶段');
      }
    });
    
    const startTime = Date.now();
    
    // 1. 页面开始加载
    console.log('1️⃣ 开始加载页面...');
    await page.goto('/');
    loadingStages.pageStart = true;
    
    // 2. 等待过场动画开始
    console.log('2️⃣ 等待过场动画开始...');
    const pixelLoader = page.locator('.pixel-loader-animation').first();
    await expect(pixelLoader).toBeVisible({ timeout: 5000 });
    
    // 验证动画元素存在
    const progressBar = page.locator('.bg-gradient-to-r.from-green-400').first();
    await expect(progressBar).toBeVisible();
    console.log('✅ 过场动画正常显示');
    
    // 3. 监控动画进度，确保不卡顿
    console.log('3️⃣ 监控动画进度...');
    let lastProgress = -1;
    let stagnantCount = 0;
    const maxStagnantTime = 15; // 最多允许卡顿15次检查
    
    while (await pixelLoader.isVisible()) {
      try {
        // 获取当前进度
        const progressElement = page.locator('span:has-text("%")').first();
        const progressText = await progressElement.textContent();
        const currentProgress = parseInt(progressText?.replace('%', '') || '0');
        
        if (currentProgress > lastProgress) {
          lastProgress = currentProgress;
          stagnantCount = 0;
          console.log(`   进度: ${currentProgress}%`);
        } else {
          stagnantCount++;
        }
        
        // 如果进度长时间不变，可能卡顿了
        if (stagnantCount > maxStagnantTime) {
          console.warn(`⚠️ 动画可能卡顿，进度停留在 ${lastProgress}% 超过 ${maxStagnantTime} 次检查`);
          break;
        }
        
        await page.waitForTimeout(200);
      } catch (error) {
        // 如果获取进度失败，可能动画已经结束
        break;
      }
    }
    
    // 4. 等待动画完成
    console.log('4️⃣ 等待动画完成...');
    await expect(pixelLoader).toBeHidden({ timeout: 10000 });
    loadingStages.animationComplete = true;
    console.log('✅ 过场动画完成');
    
    // 5. 等待React应用挂载
    console.log('5️⃣ 等待React应用挂载...');
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 5000 });
    loadingStages.reactMount = true;
    console.log('✅ React应用已挂载');
    
    // 6. 等待主要组件加载
    console.log('6️⃣ 等待主要组件加载...');
    
    // 检查导航栏
    const navbar = page.locator('nav').first();
    await expect(navbar).toBeVisible({ timeout: 3000 });
    console.log('   ✅ 导航栏已加载');
    
    // 检查Hero区域
    const heroSection = page.locator('[class*="hero"], [class*="Hero"], h1').first();
    await expect(heroSection).toBeVisible({ timeout: 3000 });
    console.log('   ✅ Hero区域已加载');
    
    // 检查功能指南区域
    const featuresSection = page.locator('[class*="features"], [class*="Features"]').first();
    if (await featuresSection.count() > 0) {
      await expect(featuresSection).toBeVisible({ timeout: 3000 });
      console.log('   ✅ 功能指南区域已加载');
    }
    
    loadingStages.componentsLoaded = true;
    
    // 7. 等待页面可交互
    console.log('7️⃣ 等待页面可交互...');
    await page.waitForLoadState('networkidle');
    
    // 检查可点击元素
    const clickableElements = await page.locator('button, a, [role="button"]').count();
    console.log(`   发现 ${clickableElements} 个可交互元素`);
    
    loadingStages.interactionReady = true;
    
    const totalLoadTime = Date.now() - startTime;
    console.log(`🎉 主页完整加载完成，总耗时: ${totalLoadTime}ms`);
    
    // 验证加载时间合理（不超过15秒）
    expect(totalLoadTime).toBeLessThan(15000);
    
    // 验证所有阶段都完成
    Object.entries(loadingStages).forEach(([stage, completed]) => {
      expect(completed).toBe(true);
      console.log(`   ✅ ${stage}: 完成`);
    });
    
    console.log(`✅ 主页完整加载流程测试通过 (${browserName})`);
  });
  
  // 测试页面在网络慢速情况下的加载
  test('慢速网络环境下的主页加载', async ({ page, browserName }) => {
    console.log(`\n🐌 测试慢速网络环境 (${browserName})`);
    
    // 模拟慢速3G网络
    const client = await page.context().newCDPSession(page);
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: 500 * 1024, // 500KB/s
      uploadThroughput: 500 * 1024,
      latency: 400 // 400ms延迟
    });
    
    const startTime = Date.now();
    
    await page.goto('/');
    
    // 在慢速网络下，动画应该仍然能正常显示
    const pixelLoader = page.locator('.pixel-loader-animation').first();
    await expect(pixelLoader).toBeVisible({ timeout: 8000 });
    
    // 等待动画完成（给更多时间）
    await expect(pixelLoader).toBeHidden({ timeout: 20000 });
    
    // 等待主要内容加载
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 15000 });
    
    const loadTime = Date.now() - startTime;
    console.log(`慢速网络下加载时间: ${loadTime}ms`);
    
    // 恢复正常网络
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: -1,
      uploadThroughput: -1,
      latency: 0
    });
    
    console.log(`✅ 慢速网络测试完成 (${browserName})`);
  });
  
  // 测试页面在多次刷新后的稳定性
  test('多次页面刷新的稳定性', async ({ page, browserName }) => {
    console.log(`\n🔄 测试多次刷新稳定性 (${browserName})`);
    
    const refreshCount = 3;
    
    for (let i = 1; i <= refreshCount; i++) {
      console.log(`第 ${i}/${refreshCount} 次加载...`);
      
      const startTime = Date.now();
      
      if (i === 1) {
        await page.goto('/');
      } else {
        await page.reload();
      }
      
      // 等待动画出现
      const pixelLoader = page.locator('.pixel-loader-animation').first();
      try {
        await expect(pixelLoader).toBeVisible({ timeout: 5000 });
        console.log(`   ✅ 第${i}次：动画正常显示`);
      } catch (error) {
        console.log(`   ⚠️ 第${i}次：动画未显示（可能已缓存）`);
      }
      
      // 等待动画完成或页面直接显示
      try {
        await expect(pixelLoader).toBeHidden({ timeout: 8000 });
      } catch (error) {
        // 动画可能不存在，继续检查页面是否正常
      }
      
      // 确保页面正常加载
      await page.waitForFunction(() => {
        const root = document.querySelector('#root');
        return root && root.children.length > 0;
      }, { timeout: 5000 });
      
      const loadTime = Date.now() - startTime;
      console.log(`   第${i}次加载耗时: ${loadTime}ms`);
      
      // 验证主要元素存在
      const navbar = page.locator('nav').first();
      await expect(navbar).toBeVisible({ timeout: 2000 });
    }
    
    console.log(`✅ 多次刷新稳定性测试完成 (${browserName})`);
  });
  
  // 测试浏览器标签页切换对动画的影响
  test('浏览器标签页切换对加载流程的影响', async ({ page, browserName }) => {
    console.log(`\n🔖 测试标签页切换影响 (${browserName})`);
    
    await page.goto('/');
    
    // 等待动画开始
    const pixelLoader = page.locator('.pixel-loader-animation').first();
    await expect(pixelLoader).toBeVisible({ timeout: 3000 });
    
    // 模拟切换到其他标签页（页面失去焦点）
    await page.evaluate(() => {
      // 直接触发事件，不修改只读属性
      document.dispatchEvent(new Event('visibilitychange'));
      window.dispatchEvent(new Event('blur'));
    });
    
    console.log('模拟切换到其他标签页...');
    await page.waitForTimeout(1000);
    
    // 模拟切换回当前标签页
    await page.evaluate(() => {
      document.dispatchEvent(new Event('visibilitychange'));
      window.dispatchEvent(new Event('focus'));
    });
    
    console.log('模拟切换回当前标签页...');
    
    // 动画应该能够继续完成
    await expect(pixelLoader).toBeHidden({ timeout: 10000 });
    
    // 页面应该正常显示
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 5000 });
    
    console.log(`✅ 标签页切换测试完成 (${browserName})`);
  });
  
  // 测试JavaScript错误对加载流程的影响
  test('JavaScript错误不应阻止页面加载', async ({ page, browserName }) => {
    console.log(`\n🐛 测试JavaScript错误容错性 (${browserName})`);
    
    const jsErrors = [];
    page.on('pageerror', error => {
      jsErrors.push(error.message);
      console.log(`捕获到JS错误: ${error.message}`);
    });
    
    await page.goto('/');
    
    // 人为注入一个JavaScript错误
    await page.evaluate(() => {
      setTimeout(() => {
        // 这会产生一个错误，但不应该影响页面加载
        nonExistentFunction();
      }, 100);
    });
    
    // 尽管有错误，动画和页面加载应该继续
    const pixelLoader = page.locator('.pixel-loader-animation').first();
    await expect(pixelLoader).toBeVisible({ timeout: 5000 });
    await expect(pixelLoader).toBeHidden({ timeout: 10000 });
    
    // 页面应该正常显示
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 5000 });
    
    // 验证导航栏存在
    const navbar = page.locator('nav').first();
    await expect(navbar).toBeVisible({ timeout: 2000 });
    
    console.log(`捕获到 ${jsErrors.length} 个JavaScript错误，但页面正常加载`);
    console.log(`✅ JavaScript错误容错性测试完成 (${browserName})`);
  });
  
});