import { test, expect } from '@playwright/test';

/**
 * 背景闪烁修复验证测试
 */
test.describe('背景闪烁修复验证', () => {

  test('验证页面加载时无背景闪烁', async ({ page, browserName }) => {
    console.log(`\n🎯 测试背景闪烁修复 (${browserName})`);
    
    // 监听背景颜色变化
    const backgroundColors = [];
    
    // 在页面加载前设置监听器
    await page.addInitScript(() => {
      window.backgroundObserver = [];
      
      // 监听body背景颜色变化
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
            const bgColor = window.getComputedStyle(document.body).backgroundColor;
            window.backgroundObserver.push({
              timestamp: Date.now(),
              backgroundColor: bgColor,
              element: 'body'
            });
          }
        });
      });
      
      // 开始监听
      setTimeout(() => {
        if (document.body) {
          observer.observe(document.body, { attributes: true, attributeFilter: ['style'] });
          observer.observe(document.documentElement, { attributes: true, attributeFilter: ['style'] });
        }
      }, 0);
      
      // 每100ms记录一次背景颜色
      const interval = setInterval(() => {
        if (document.body) {
          const bodyBg = window.getComputedStyle(document.body).backgroundColor;
          const htmlBg = window.getComputedStyle(document.documentElement).backgroundColor;
          const rootBg = window.getComputedStyle(document.querySelector('#root') || document.body).backgroundColor;
          
          window.backgroundObserver.push({
            timestamp: Date.now(),
            bodyBackground: bodyBg,
            htmlBackground: htmlBg,
            rootBackground: rootBg
          });
        }
      }, 100);
      
      // 5秒后停止监听
      setTimeout(() => {
        clearInterval(interval);
      }, 5000);
    });
    
    // 快速导航到页面以捕获加载过程
    const startTime = Date.now();
    await page.goto('/');
    
    // 等待页面完全加载
    await page.waitForSelector('nav', { timeout: 10000 });
    await page.waitForSelector('#home h1', { timeout: 5000 });
    
    // 等待一段时间确保所有背景变化都被记录
    await page.waitForTimeout(2000);
    
    // 获取背景颜色变化记录
    const colorObservations = await page.evaluate(() => {
      return window.backgroundObserver || [];
    });
    
    console.log(`记录到 ${colorObservations.length} 次背景颜色检查`);
    
    // 分析背景颜色变化
    const uniqueColors = new Set();
    colorObservations.forEach(obs => {
      if (obs.bodyBackground) uniqueColors.add(obs.bodyBackground);
      if (obs.htmlBackground) uniqueColors.add(obs.htmlBackground);
      if (obs.rootBackground) uniqueColors.add(obs.rootBackground);
    });
    
    console.log('检测到的背景颜色:', Array.from(uniqueColors));
    
    // 验证背景颜色一致性
    const blackVariants = [
      'rgb(0, 0, 0)',           // 纯黑色
      'rgba(0, 0, 0, 1)',       // 透明度为1的黑色
      'transparent',            // 透明（应该继承黑色）
    ];
    
    const validColors = Array.from(uniqueColors).filter(color => 
      blackVariants.includes(color) || color === '' || !color
    );
    
    // 不应该有白色或其他颜色的背景闪烁
    const invalidColors = Array.from(uniqueColors).filter(color => 
      color && 
      !blackVariants.includes(color) && 
      !color.startsWith('rgba(0, 0, 0') &&
      color !== 'transparent'
    );
    
    console.log('有效的背景颜色:', validColors);
    console.log('无效的背景颜色 (闪烁):', invalidColors);
    
    // 验证没有背景闪烁（没有非黑色背景）
    expect(invalidColors).toHaveLength(0);
    
    console.log('✅ 背景颜色保持一致，无闪烁现象');
  });

  test('验证加载动画过渡平滑', async ({ page, browserName }) => {
    console.log(`\n🔄 测试加载动画过渡平滑度 (${browserName})`);
    
    await page.goto('/');
    
    // 检查是否有加载器出现
    const loaderAppeared = await page.waitForSelector('.fixed.inset-0.z-50', { 
      timeout: 3000,
      state: 'visible'
    }).then(() => true).catch(() => false);
    
    if (loaderAppeared) {
      console.log('✅ 加载器正确显示');
      
      // 检查加载器的过渡效果
      const loaderElement = page.locator('.fixed.inset-0.z-50').first();
      
      // 验证加载器有正确的过渡类
      const hasTransition = await loaderElement.evaluate(el => {
        const style = window.getComputedStyle(el);
        return style.transitionProperty.includes('opacity');
      });
      
      expect(hasTransition).toBe(true);
      console.log('✅ 加载器具有透明度过渡效果');
      
      // 等待加载器消失
      await expect(loaderElement).not.toBeVisible({ timeout: 10000 });
      console.log('✅ 加载器平滑消失');
    } else {
      console.log('⚡ 页面加载非常快，未显示加载器');
    }
    
    // 验证主内容正确显示
    await expect(page.locator('nav')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('#home h1')).toBeVisible({ timeout: 5000 });
    
    console.log('✅ 主内容正确显示，过渡平滑');
  });

  test('验证不同性能模式下的背景一致性', async ({ page, browserName }) => {
    console.log(`\n⚡ 测试不同性能模式背景一致性 (${browserName})`);
    
    // 测试不同的性能标志
    const performanceFlags = [
      { 'emergency-mode': true },
      { 'minimal-ui': true },
      { 'reduce-animations': true },
      {} // 正常模式
    ];
    
    for (const flags of performanceFlags) {
      const flagName = Object.keys(flags)[0] || 'normal';
      console.log(`测试性能模式: ${flagName}`);
      
      // 设置性能标志
      await page.addInitScript((flags) => {
        window.PERFORMANCE_FLAGS = flags;
      }, flags);
      
      await page.goto('/');
      
      // 检查背景颜色
      const bodyColor = await page.evaluate(() => {
        return window.getComputedStyle(document.body).backgroundColor;
      });
      
      const htmlColor = await page.evaluate(() => {
        return window.getComputedStyle(document.documentElement).backgroundColor;
      });
      
      console.log(`${flagName} 模式 - body: ${bodyColor}, html: ${htmlColor}`);
      
      // 验证背景是黑色或透明
      const isValidColor = (color) => {
        return color === 'rgb(0, 0, 0)' || 
               color === 'rgba(0, 0, 0, 1)' || 
               color === 'transparent' ||
               color === '' ||
               !color;
      };
      
      expect(isValidColor(bodyColor)).toBe(true);
      expect(isValidColor(htmlColor)).toBe(true);
      
      // 等待页面稳定
      await page.waitForSelector('nav', { timeout: 10000 });
    }
    
    console.log('✅ 所有性能模式下背景颜色一致');
  });

  test('验证CSS过渡时间优化', async ({ page, browserName }) => {
    console.log(`\n⏱️ 测试CSS过渡时间优化 (${browserName})`);
    
    await page.goto('/');
    
    // 等待页面加载
    await page.waitForSelector('nav', { timeout: 10000 });
    
    // 检查关键元素的过渡设置
    const transitionSettings = await page.evaluate(() => {
      const results = {};
      
      // 检查主容器的过渡设置
      const mainContainer = document.querySelector('.min-h-screen.bg-black');
      if (mainContainer) {
        const style = window.getComputedStyle(mainContainer);
        results.mainContainer = {
          transitionProperty: style.transitionProperty,
          transitionDuration: style.transitionDuration
        };
      }
      
      // 检查根元素
      const root = document.querySelector('#root');
      if (root) {
        const style = window.getComputedStyle(root);
        results.root = {
          backgroundColor: style.backgroundColor,
          transition: style.transition
        };
      }
      
      return results;
    });
    
    console.log('过渡设置检查:', transitionSettings);
    
    // 验证主容器有正确的过渡设置
    if (transitionSettings.mainContainer) {
      expect(transitionSettings.mainContainer.transitionProperty).toContain('opacity');
      console.log('✅ 主容器具有透明度过渡');
    }
    
    // 验证根元素背景色正确
    if (transitionSettings.root) {
      expect(transitionSettings.root.backgroundColor).toBe('rgb(0, 0, 0)');
      console.log('✅ 根元素背景色正确');
    }
    
    console.log('✅ CSS过渡时间优化验证完成');
  });

});