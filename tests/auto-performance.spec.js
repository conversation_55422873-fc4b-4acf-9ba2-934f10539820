import { test, expect } from '@playwright/test';

// 自动性能优化测试
test.describe('自动性能优化系统测试', () => {

  test('高性能设备应获得完整体验', async ({ page, browserName }) => {
    console.log(`\n🚀 测试高性能设备体验 (${browserName})`);
    
    await page.goto('/');
    
    // 等待自动性能检测完成
    await page.waitForFunction(() => {
      return window.autoPerformanceManager && window.autoPerformanceManager.isInitialized;
    }, { timeout: 10000 });
    
    // 检查性能级别
    const performanceStatus = await page.evaluate(() => {
      return window.autoPerformanceManager.getPerformanceStatus();
    });
    
    console.log('检测到的性能级别:', performanceStatus.currentLevel);
    console.log('设备评分:', performanceStatus.deviceCapabilities?.overallScore);
    
    // 高性能设备应该获得minimal或balanced优化（即较少的限制）
    expect(['minimal', 'balanced']).toContain(performanceStatus.currentLevel);
    
    // 等待加载器完成
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 15000 });
    
    console.log(`✅ 高性能设备测试完成，优化级别: ${performanceStatus.currentLevel}`);
  });

  test('模拟低性能设备应自动降级', async ({ page, browserName }) => {
    console.log(`\n📱 测试低性能设备自动降级 (${browserName})`);
    
    // 获取CDP会话用于模拟低性能环境
    const client = await page.context().newCDPSession(page);
    
    // 模拟低性能设备
    await client.send('Emulation.setCPUThrottlingRate', { rate: 4 }); // CPU节流
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: 500 * 1024, // 慢速网络
      uploadThroughput: 100 * 1024,
      latency: 500
    });
    
    await page.goto('/');
    
    // 等待性能检测完成
    await page.waitForFunction(() => {
      return window.autoPerformanceManager && window.autoPerformanceManager.isInitialized;
    }, { timeout: 15000 });
    
    // 检查是否自动应用了更高级别的优化
    const performanceStatus = await page.evaluate(() => {
      return window.autoPerformanceManager.getPerformanceStatus();
    });
    
    console.log('低性能环境检测结果:', performanceStatus.currentLevel);
    console.log('应用的优化标志:', performanceStatus.activeFlags);
    
    // 低性能环境应该应用aggressive或maximum优化
    expect(['aggressive', 'maximum']).toContain(performanceStatus.currentLevel);
    
    // 检查是否应用了相应的CSS类
    const bodyClasses = await page.evaluate(() => document.body.className);
    console.log('应用的CSS类:', bodyClasses);
    expect(bodyClasses).toMatch(/auto-perf-(aggressive|maximum)/);
    
    // 页面应该仍然能够加载
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 20000 });
    
    // 恢复正常性能设置
    await client.send('Emulation.setCPUThrottlingRate', { rate: 1 });
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: -1,
      uploadThroughput: -1,
      latency: 0
    });
    
    console.log(`✅ 低性能设备自动降级测试完成`);
  });

  test('自适应加载器根据性能自动调整', async ({ page, browserName }) => {
    console.log(`\n🎨 测试自适应加载器 (${browserName})`);
    
    await page.goto('/');
    
    // 检测加载器是否出现
    const loaderAppeared = await page.waitForSelector('.fixed.inset-0.z-50', { 
      timeout: 5000 
    }).catch(() => null);
    
    if (loaderAppeared) {
      console.log('✅ 自适应加载器正常显示');
      
      // 检查是否有像素网格（取决于性能级别）
      const hasPixelGrid = await page.locator('.grid.gap-1').count();
      console.log(`像素网格元素数量: ${hasPixelGrid}`);
      
      // 等待加载完成
      await page.waitForFunction(() => {
        return !document.querySelector('.fixed.inset-0.z-50');
      }, { timeout: 15000 });
      
      console.log('✅ 加载器正常完成');
    } else {
      console.log('⚡ 加载器快速模式 - 立即跳过动画');
    }
    
    // 验证页面最终加载成功
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 10000 });
    
    console.log(`✅ 自适应加载器测试完成`);
  });

  test('实时性能监控和自动调整', async ({ page, browserName }) => {
    console.log(`\n📊 测试实时性能监控 (${browserName})`);
    
    await page.goto('/');
    
    // 等待性能管理器初始化
    await page.waitForFunction(() => {
      return window.autoPerformanceManager && window.autoPerformanceManager.isInitialized;
    }, { timeout: 10000 });
    
    // 获取初始性能状态
    const initialStatus = await page.evaluate(() => {
      return window.autoPerformanceManager.getPerformanceStatus();
    });
    
    console.log('初始性能级别:', initialStatus.currentLevel);
    
    // 等待一段时间让实时监控工作
    await page.waitForTimeout(5000);
    
    // 检查性能指标是否在更新
    const currentMetrics = await page.evaluate(() => {
      return window.autoPerformanceManager.getPerformanceStatus().performanceMetrics;
    });
    
    console.log('当前性能指标:', currentMetrics);
    
    // FPS应该有合理的值
    expect(currentMetrics.fps).toBeGreaterThan(10);
    expect(currentMetrics.fps).toBeLessThan(120);
    
    // 检查优化历史是否被记录
    const optimizationHistory = await page.evaluate(() => {
      return window.autoPerformanceManager.getPerformanceStatus().optimizationHistory;
    });
    
    console.log('优化历史记录:', optimizationHistory);
    expect(optimizationHistory.length).toBeGreaterThan(0);
    
    console.log(`✅ 实时性能监控测试完成`);
  });

  test('紧急优化模式触发测试', async ({ page, browserName }) => {
    console.log(`\n🚨 测试紧急优化模式 (${browserName})`);
    
    // 极度限制资源模拟超低性能设备
    const client = await page.context().newCDPSession(page);
    await client.send('Emulation.setCPUThrottlingRate', { rate: 8 }); // 8倍CPU节流
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: 50 * 1024, // 极慢网络 50KB/s
      uploadThroughput: 25 * 1024,
      latency: 1000 // 1秒延迟
    });
    
    await page.goto('/');
    
    // 等待性能检测完成（需要更长时间）
    await page.waitForFunction(() => {
      return window.autoPerformanceManager && window.autoPerformanceManager.isInitialized;
    }, { timeout: 20000 });
    
    const performanceStatus = await page.evaluate(() => {
      return window.autoPerformanceManager.getPerformanceStatus();
    });
    
    console.log('极低性能环境检测结果:', performanceStatus.currentLevel);
    console.log('设备评分:', performanceStatus.deviceCapabilities?.overallScore);
    
    // 极低性能环境应该触发maximum优化
    if (performanceStatus.deviceCapabilities?.overallScore < 35) {
      expect(performanceStatus.currentLevel).toBe('maximum');
      
      // 检查是否应用了紧急模式标志
      expect(performanceStatus.activeFlags['emergency-mode']).toBe(true);
    }
    
    // 即使在极端条件下，页面也应该能加载
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 30000 });
    
    // 恢复正常设置
    await client.send('Emulation.setCPUThrottlingRate', { rate: 1 });
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: -1,
      uploadThroughput: -1,
      latency: 0
    });
    
    console.log(`✅ 紧急优化模式测试完成`);
  });

  test('用户无感知的优化过程', async ({ page, browserName }) => {
    console.log(`\n🔍 测试用户无感知优化 (${browserName})`);
    
    await page.goto('/');
    
    // 检查页面上没有显示优化相关的用户界面
    const optimizationUI = await page.locator('text=/性能|优化|模式/').count();
    
    // 在生产模式下，用户不应该看到任何优化相关的UI
    if (process.env.NODE_ENV === 'production') {
      expect(optimizationUI).toBe(0);
    }
    
    // 等待加载完成
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 15000 });
    
    // 验证核心功能正常工作
    const navbar = page.locator('nav').first();
    await expect(navbar).toBeVisible({ timeout: 5000 });
    
    // 检查是否有可交互元素
    const interactiveElements = await page.locator('button, a, [role="button"]').count();
    expect(interactiveElements).toBeGreaterThan(0);
    
    console.log(`✅ 用户无感知优化测试完成，发现 ${interactiveElements} 个交互元素`);
  });

  test('不同Chrome版本的自动适配', async ({ page, browserName }) => {
    console.log(`\n🌐 测试Chrome版本自动适配 (${browserName})`);
    
    await page.goto('/');
    
    // 等待性能检测完成
    await page.waitForFunction(() => {
      return window.autoPerformanceManager && window.autoPerformanceManager.isInitialized;
    }, { timeout: 10000 });
    
    // 获取浏览器能力检测结果
    const browserCapabilities = await page.evaluate(() => {
      const status = window.autoPerformanceManager.getPerformanceStatus();
      return status.deviceCapabilities?.browser;
    });
    
    console.log('浏览器能力检测:', browserCapabilities);
    
    // 验证关键能力检测
    expect(typeof browserCapabilities.webgl).toBe('boolean');
    expect(typeof browserCapabilities.intersectionObserver).toBe('boolean');
    expect(typeof browserCapabilities.es6).toBe('boolean');
    
    // 根据浏览器能力，系统应该自动适配
    const performanceFlags = await page.evaluate(() => {
      return window.PERFORMANCE_FLAGS || {};
    });
    
    console.log('应用的性能标志:', performanceFlags);
    
    // 确保页面在当前浏览器版本中正常工作
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 15000 });
    
    console.log(`✅ Chrome版本自动适配测试完成`);
  });

});