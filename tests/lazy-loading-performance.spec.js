import { test, expect } from '@playwright/test';

/**
 * 按需加载性能测试
 * 验证 Web IDE 和智能对话的按需加载功能
 */
test.describe('按需加载性能测试', () => {

  test('初始页面加载时不应该加载 Web IDE 和智能对话', async ({ page, browserName }) => {
    console.log(`\n⚡ 测试初始页面性能优化 (${browserName})`);
    
    // 监听网络请求
    const requests = [];
    page.on('request', request => {
      requests.push(request.url());
    });

    await page.goto('/');
    
    // 等待页面基础内容加载完成
    await page.waitForSelector('nav', { timeout: 10000 });
    await page.waitForSelector('#home h1', { timeout: 5000 });
    
    console.log('页面基础内容加载完成');
    
    // 验证 Web IDE 和智能对话的按需加载按钮存在
    const webIDEButton = page.locator('text=启动 Web IDE').first();
    const chatButton = page.locator('text=启动智能对话').first();
    
    // 等待按钮出现
    await expect(webIDEButton).toBeVisible({ timeout: 5000 });
    await expect(chatButton).toBeVisible({ timeout: 5000 });
    
    console.log('✅ Web IDE 和智能对话的启动按钮显示正确');
    
    // 验证实际的组件没有被加载
    const webIDEIframe = page.locator('iframe').first();
    const chatIframe = page.locator('iframe').nth(1);
    
    // 这两个 iframe 应该不存在或不可见
    await expect(webIDEIframe).not.toBeVisible();
    await expect(chatIframe).not.toBeVisible();
    
    console.log('✅ Web IDE 和智能对话组件未被预加载');
    
    // 统计页面初始加载的资源数量
    const initialRequestCount = requests.length;
    console.log(`初始页面请求数量: ${initialRequestCount}`);
    
    // 验证请求数量相比之前有所减少（预期效果）
    expect(initialRequestCount).toBeLessThan(60); // 调整预期值
    
    console.log(`✅ 初始页面性能优化测试完成 (${browserName})`);
  });

  test('点击启动按钮应该按需加载 Web IDE', async ({ page, browserName }) => {
    console.log(`\n🚀 测试 Web IDE 按需加载 (${browserName})`);
    
    await page.goto('/');
    
    // 等待页面加载
    await page.waitForSelector('nav', { timeout: 10000 });
    
    // 滚动到 Web IDE 部分
    await page.locator('#webide').scrollIntoViewIfNeeded();
    
    // 检查按钮状态 - 区分登录和未登录状态
    const loginButton = page.locator('text=登录后使用').first();
    const webIDEStartButton = page.locator('text=启动 Web IDE').first();
    
    if (await loginButton.isVisible()) {
      console.log('用户未登录，验证登录提示');
      await expect(loginButton).toBeVisible();
      
      // 点击登录按钮应该打开登录模态框
      await loginButton.click();
      await expect(page.locator('[class*="modal"], [class*="Modal"]')).toBeVisible({ timeout: 3000 });
      
      console.log('✅ 未登录用户看到正确的登录提示');
    } else if (await webIDEStartButton.isVisible()) {
      console.log('用户已登录，测试按需加载功能');
      
      // 记录点击前的状态
      const beforeClickRequestCount = await page.evaluate(() => 
        performance.getEntriesByType('resource').length
      );
      
      // 点击启动 Web IDE 按钮
      await webIDEStartButton.click();
      
      // 等待 Web IDE 组件开始加载
      await expect(page.locator('text=Loading...').or(page.locator('[class*="loading"]'))).toBeVisible({ timeout: 5000 });
      
      console.log('✅ Web IDE 组件开始按需加载');
      
      // 记录点击后的资源请求
      const afterClickRequestCount = await page.evaluate(() => 
        performance.getEntriesByType('resource').length
      );
      
      const newRequestCount = afterClickRequestCount - beforeClickRequestCount;
      console.log(`新增资源请求数量: ${newRequestCount}`);
      
      // 验证确实有新的资源被加载
      expect(newRequestCount).toBeGreaterThan(0);
      
      console.log('✅ Web IDE 按需加载功能正常工作');
    }
  });

  test('点击启动按钮应该按需加载智能对话', async ({ page, browserName }) => {
    console.log(`\n💬 测试智能对话按需加载 (${browserName})`);
    
    await page.goto('/');
    
    // 等待页面加载
    await page.waitForSelector('nav', { timeout: 10000 });
    
    // 滚动到智能对话部分
    await page.locator('#chat').scrollIntoViewIfNeeded();
    
    // 检查按钮状态
    const loginButton = page.locator('text=登录后使用').nth(1);
    const chatStartButton = page.locator('text=启动智能对话').first();
    
    if (await loginButton.isVisible()) {
      console.log('用户未登录，验证登录提示');
      await expect(loginButton).toBeVisible();
      console.log('✅ 未登录用户看到正确的登录提示');
    } else if (await chatStartButton.isVisible()) {
      console.log('用户已登录，测试智能对话按需加载功能');
      
      // 记录点击前的状态
      const beforeClickRequestCount = await page.evaluate(() => 
        performance.getEntriesByType('resource').length
      );
      
      // 点击启动智能对话按钮
      await chatStartButton.click();
      
      // 等待智能对话组件开始加载
      await expect(page.locator('text=Loading...').or(page.locator('[class*="loading"]'))).toBeVisible({ timeout: 5000 });
      
      console.log('✅ 智能对话组件开始按需加载');
      
      // 记录点击后的资源请求
      const afterClickRequestCount = await page.evaluate(() => 
        performance.getEntriesByType('resource').length
      );
      
      const newRequestCount = afterClickRequestCount - beforeClickRequestCount;
      console.log(`新增资源请求数量: ${newRequestCount}`);
      
      // 验证确实有新的资源被加载
      expect(newRequestCount).toBeGreaterThan(0);
      
      console.log('✅ 智能对话按需加载功能正常工作');
    }
  });

  test('按需加载应该与自动性能优化系统协调工作', async ({ page, browserName }) => {
    console.log(`\n🔧 测试按需加载与性能优化的协调 (${browserName})`);
    
    await page.goto('/');
    
    // 等待自动性能管理器初始化
    await page.waitForFunction(() => {
      return window.autoPerformanceManager && window.autoPerformanceManager.isInitialized;
    }, { timeout: 10000 });
    
    // 获取性能优化状态
    const performanceStatus = await page.evaluate(() => {
      return window.autoPerformanceManager.getPerformanceStatus();
    });
    
    console.log('当前性能优化级别:', performanceStatus.currentLevel);
    console.log('设备评分:', performanceStatus.deviceCapabilities?.overallScore);
    
    // 验证页面基本结构正确
    await expect(page.locator('#webide')).toBeVisible();
    await expect(page.locator('#chat')).toBeVisible();
    
    // 验证按需加载按钮存在且工作正常
    const webIDESection = page.locator('#webide');
    const chatSection = page.locator('#chat');
    
    await webIDESection.scrollIntoViewIfNeeded();
    await expect(webIDESection.locator('button').first()).toBeVisible();
    
    await chatSection.scrollIntoViewIfNeeded();
    await expect(chatSection.locator('button').first()).toBeVisible();
    
    console.log('✅ 按需加载与自动性能优化系统协调正常');
    
    // 验证在不同性能级别下，按需加载仍然正常工作
    const pageMetrics = await page.evaluate(() => {
      return {
        loadEventEnd: performance.timing?.loadEventEnd || Date.now(),
        navigationStart: performance.timing?.navigationStart || Date.now(),
        resourceCount: performance.getEntriesByType('resource').length
      };
    });
    
    const loadTime = pageMetrics.loadEventEnd - pageMetrics.navigationStart;
    console.log(`页面加载时间: ${loadTime}ms`);
    console.log(`资源请求数量: ${pageMetrics.resourceCount}`);
    
    // 由于采用了按需加载，页面加载时间应该有所改善
    expect(loadTime).toBeLessThan(3000); // 3秒内完成加载
    
    console.log(`✅ 按需加载性能优化测试完成 (${browserName})`);
  });

  test('验证按需加载的用户体验', async ({ page, browserName }) => {
    console.log(`\n🎯 测试按需加载的用户体验 (${browserName})`);
    
    await page.goto('/');
    
    // 等待页面加载
    await page.waitForSelector('nav', { timeout: 10000 });
    
    // 验证按钮设计和文案
    const webIDECard = page.locator('#webide .bg-gray-900\\/50').first();
    const chatCard = page.locator('#chat .bg-gray-900\\/50').first();
    
    // 滚动并检查 Web IDE 卡片
    await page.locator('#webide').scrollIntoViewIfNeeded();
    await expect(webIDECard).toBeVisible();
    
    // 验证 Web IDE 卡片内容
    await expect(webIDECard.locator('text=准备启动 Web IDE')).toBeVisible();
    await expect(webIDECard.locator('text=点击下方按钮加载在线开发环境')).toBeVisible();
    
    // 滚动并检查智能对话卡片
    await page.locator('#chat').scrollIntoViewIfNeeded();
    await expect(chatCard).toBeVisible();
    
    // 验证智能对话卡片内容
    await expect(chatCard.locator('text=准备启动智能对话')).toBeVisible();
    await expect(chatCard.locator('text=点击下方按钮加载 LobeChat')).toBeVisible();
    
    console.log('✅ 按需加载卡片设计正确');
    
    // 测试按钮的视觉效果和响应
    const webIDEButton = webIDECard.locator('button').first();
    const chatButton = chatCard.locator('button').first();
    
    // 检查按钮样式类
    const webIDEButtonClass = await webIDEButton.getAttribute('class');
    const chatButtonClass = await chatButton.getAttribute('class');
    
    // 验证按钮有正确的样式类（gradient, hover effects等）
    expect(webIDEButtonClass).toContain('bg-gradient-to-r');
    expect(chatButtonClass).toContain('bg-gradient-to-r');
    
    console.log('✅ 按钮样式和交互效果正确');
    
    // 验证图标显示正确
    await expect(webIDECard.locator('svg').first()).toBeVisible();
    await expect(chatCard.locator('svg').first()).toBeVisible();
    
    console.log('✅ 图标显示正确');
    
    console.log(`✅ 按需加载用户体验测试完成 (${browserName})`);
  });

});