import { test, expect } from '@playwright/test';

// 动画加载和兼容性专项测试
test.describe('过场动画兼容性测试', () => {
  
  // 测试动画基础加载流程
  test('PixelLoader 过场动画正常加载和完成', async ({ page, browserName }) => {
    console.log(`\n🎬 测试过场动画 (${browserName})`);
    
    // 监听控制台错误和警告
    const consoleMessages = [];
    page.on('console', msg => {
      if (msg.type() === 'error' || msg.type() === 'warn') {
        consoleMessages.push(`[${msg.type()}] ${msg.text()}`);
      }
    });
    
    // 监听页面错误
    const pageErrors = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
    });
    
    // 记录开始时间
    const startTime = Date.now();
    
    // 访问页面
    await page.goto('/');
    
    // 等待 PixelLoader 出现
    console.log('等待过场动画出现...');
    const pixelLoader = page.locator('.pixel-loader-animation').first();
    await expect(pixelLoader).toBeVisible({ timeout: 3000 });
    console.log('✅ 过场动画已出现');
    
    // 检查动画元素是否正确渲染
    const pixelElements = page.locator('.pixel-loader-animation .grid > div');
    const pixelCount = await pixelElements.count();
    console.log(`发现 ${pixelCount} 个像素动画元素`);
    expect(pixelCount).toBeGreaterThan(0);
    
    // 检查进度条是否存在（更精确的选择器）
    const progressBar = page.locator('.h-full.bg-gradient-to-r.from-green-400');
    await expect(progressBar).toBeVisible({ timeout: 2000 });
    console.log('✅ 进度条正常显示');
    
    // 等待动画完成 - 监控进度变化
    console.log('监控动画进度...');
    let currentProgress = 0;
    let progressStuckCount = 0;
    const maxStuckCount = 10; // 防止无限循环
    
    while (currentProgress < 100 && progressStuckCount < maxStuckCount) {
      try {
        // 获取当前进度
        const progressText = await page.locator('span:has-text("%")').first().textContent();
        const newProgress = parseInt(progressText?.replace('%', '') || '0');
        
        if (newProgress > currentProgress) {
          currentProgress = newProgress;
          progressStuckCount = 0; // 重置卡顿计数
          console.log(`进度更新: ${currentProgress}%`);
        } else {
          progressStuckCount++;
          console.log(`进度未变化 (${progressStuckCount}/${maxStuckCount}): ${currentProgress}%`);
        }
        
        // 短暂等待后再次检查
        await page.waitForTimeout(500);
        
        // 检查是否已经加载完成（PixelLoader 消失）
        const isLoaderVisible = await pixelLoader.isVisible();
        if (!isLoaderVisible) {
          console.log('✅ 过场动画已完成（元素已消失）');
          break;
        }
        
      } catch (error) {
        console.warn('检查进度时出错:', error.message);
        progressStuckCount++;
      }
    }
    
    // 等待过场动画完全消失
    console.log('等待过场动画消失...');
    await expect(pixelLoader).toBeHidden({ timeout: 8000 });
    
    const animationDuration = Date.now() - startTime;
    console.log(`🎬 动画完成，耗时: ${animationDuration}ms`);
    
    // 验证动画没有卡住（应该在合理时间内完成）
    expect(animationDuration).toBeLessThan(10000); // 10秒内完成
    
    // 检查是否有动画相关错误
    const animationErrors = consoleMessages.filter(msg => 
      msg.includes('PixelLoader') || 
      msg.includes('animation') || 
      msg.includes('transform') ||
      msg.includes('transition')
    );
    
    if (animationErrors.length > 0) {
      console.warn('动画相关警告:', animationErrors);
    }
    
    if (pageErrors.length > 0) {
      console.error('页面错误:', pageErrors);
      // 不要因为一般性错误而失败，但要记录
    }
    
    console.log(`✅ 过场动画测试完成 (${browserName})`);
  });
  
  // 测试动画超时处理机制
  test('动画超时保护机制正常工作', async ({ page, browserName }) => {
    console.log(`\n⏰ 测试动画超时保护 (${browserName})`);
    
    // 监听安全超时警告
    let safetyTimeoutTriggered = false;
    page.on('console', msg => {
      if (msg.text().includes('安全超时触发') || msg.text().includes('强制完成加载')) {
        safetyTimeoutTriggered = true;
        console.log('✅ 检测到安全超时保护机制触发');
      }
    });
    
    await page.goto('/');
    
    // 等待动画开始
    const pixelLoader = page.locator('.pixel-loader-animation').first();
    await expect(pixelLoader).toBeVisible({ timeout: 3000 });
    
    // 等待动画完成（应该在超时时间内完成）
    await expect(pixelLoader).toBeHidden({ timeout: 12000 }); // 给超时机制更多时间
    
    // 检查页面是否正常加载
    await page.waitForLoadState('networkidle');
    
    console.log(`✅ 超时保护测试完成，页面正常加载 (${browserName})`);
  });
  
  // 测试动画在低性能环境下的表现
  test('动画在CPU节流环境下的表现', async ({ page, browserName }) => {
    console.log(`\n🐌 测试低性能环境下的动画 (${browserName})`);
    
    // 模拟低性能环境 - CPU节流
    const client = await page.context().newCDPSession(page);
    await client.send('Emulation.setCPUThrottlingRate', { rate: 4 }); // 4倍CPU节流
    
    const startTime = Date.now();
    
    await page.goto('/');
    
    // 等待动画出现
    const pixelLoader = page.locator('.pixel-loader-animation').first();
    await expect(pixelLoader).toBeVisible({ timeout: 5000 });
    
    // 在低性能环境下，动画仍应能完成
    await expect(pixelLoader).toBeHidden({ timeout: 15000 }); // 给更多时间
    
    const totalTime = Date.now() - startTime;
    console.log(`低性能环境下动画完成时间: ${totalTime}ms`);
    
    // 即使在低性能环境下，也不应该超过15秒
    expect(totalTime).toBeLessThan(15000);
    
    // 恢复正常CPU性能
    await client.send('Emulation.setCPUThrottlingRate', { rate: 1 });
    
    console.log(`✅ 低性能环境测试完成 (${browserName})`);
  });
  
  // 测试动画CSS属性兼容性
  test('动画CSS属性在老版本浏览器中的兼容性', async ({ page, browserName }) => {
    console.log(`\n🎨 测试CSS动画属性兼容性 (${browserName})`);
    
    await page.goto('/');
    
    // 等待动画元素出现
    const pixelLoader = page.locator('.pixel-loader-animation').first();
    await expect(pixelLoader).toBeVisible({ timeout: 3000 });
    
    // 检查关键CSS属性是否正确应用
    const animationStyles = await page.evaluate(() => {
      const loader = document.querySelector('.pixel-loader-animation');
      const firstPixel = document.querySelector('.pixel-loader-animation .grid > div');
      const progressBar = document.querySelector('.bg-gradient-to-r.from-green-400');
      
      if (!loader || !firstPixel || !progressBar) {
        return { error: 'Required elements not found' };
      }
      
      const loaderStyles = window.getComputedStyle(loader);
      const pixelStyles = window.getComputedStyle(firstPixel);
      const progressStyles = window.getComputedStyle(progressBar);
      
      return {
        // 检查关键动画属性
        transforms: {
          supported: 'transform' in firstPixel.style,
          value: pixelStyles.transform
        },
        transitions: {
          supported: 'transition' in firstPixel.style,
          duration: pixelStyles.transitionDuration,
          delay: pixelStyles.transitionDelay
        },
        gradients: {
          background: progressStyles.backgroundImage,
          hasGradient: progressStyles.backgroundImage.includes('gradient')
        },
        opacity: {
          loader: loaderStyles.opacity,
          pixel: pixelStyles.opacity
        },
        boxShadow: {
          supported: 'boxShadow' in firstPixel.style,
          value: pixelStyles.boxShadow
        }
      };
    });
    
    console.log('CSS 属性支持情况:', JSON.stringify(animationStyles, null, 2));
    
    // 验证关键属性支持
    expect(animationStyles.transforms.supported).toBe(true);
    expect(animationStyles.transitions.supported).toBe(true);
    expect(animationStyles.gradients.hasGradient).toBe(true);
    
    // 等待动画完成
    await expect(pixelLoader).toBeHidden({ timeout: 10000 });
    
    console.log(`✅ CSS属性兼容性测试完成 (${browserName})`);
  });
  
  // 测试动画内存使用情况
  test('动画内存使用和清理', async ({ page, browserName }) => {
    console.log(`\n💾 测试动画内存使用 (${browserName})`);
    
    // 获取初始内存使用情况
    const initialMetrics = await page.evaluate(() => {
      if (performance.memory) {
        return {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize
        };
      }
      return null;
    });
    
    await page.goto('/');
    
    // 等待动画完成
    const pixelLoader = page.locator('.pixel-loader-animation').first();
    await expect(pixelLoader).toBeVisible({ timeout: 3000 });
    await expect(pixelLoader).toBeHidden({ timeout: 10000 });
    
    // 强制垃圾回收（如果可能）
    await page.evaluate(() => {
      if (window.gc) {
        window.gc();
      }
    });
    
    // 检查最终内存使用情况
    const finalMetrics = await page.evaluate(() => {
      if (performance.memory) {
        return {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize
        };
      }
      return null;
    });
    
    if (initialMetrics && finalMetrics) {
      const memoryIncrease = finalMetrics.usedJSHeapSize - initialMetrics.usedJSHeapSize;
      console.log(`内存使用变化: ${memoryIncrease / 1024 / 1024} MB`);
      
      // 内存增长不应该过多（调整阈值：50MB，考虑到React应用的正常内存使用）
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    } else {
      console.log('浏览器不支持性能内存监控，跳过内存检查');
    }
    
    console.log(`✅ 内存使用测试完成 (${browserName})`);
  });
  
  // 测试动画与React渲染的协调
  test('动画与React组件渲染的协调性', async ({ page, browserName }) => {
    console.log(`\n⚛️ 测试动画与React渲染协调 (${browserName})`);
    
    let reactRenderComplete = false;
    let animationComplete = false;
    
    // 监听React相关消息
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('React 组件渲染正常') || text.includes('✅')) {
        reactRenderComplete = true;
      }
      if (text.includes('加载已完成') || text.includes('启动完成')) {
        animationComplete = true;
      }
    });
    
    await page.goto('/');
    
    // 等待动画开始
    const pixelLoader = page.locator('.pixel-loader-animation').first();
    await expect(pixelLoader).toBeVisible({ timeout: 3000 });
    
    // 等待动画完成
    await expect(pixelLoader).toBeHidden({ timeout: 10000 });
    
    // 确保React应用已经正常渲染
    await page.waitForFunction(() => {
      const root = document.querySelector('#root');
      return root && root.children.length > 0;
    }, { timeout: 5000 });
    
    // 检查主要React组件是否正常渲染
    const mainComponents = [
      'nav', // Navbar
      '[class*="hero"]', // HeroSection  
      '[class*="features"]' // FeaturesGuideSection
    ];
    
    for (const selector of mainComponents) {
      const component = page.locator(selector).first();
      if (await component.count() > 0) {
        await expect(component).toBeVisible({ timeout: 2000 });
        console.log(`✅ ${selector} 组件正常渲染`);
      }
    }
    
    console.log(`✅ React协调性测试完成 (${browserName})`);
  });
  
});