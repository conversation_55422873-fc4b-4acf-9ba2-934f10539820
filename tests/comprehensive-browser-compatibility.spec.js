import { test, expect } from '@playwright/test';

// 综合浏览器兼容性测试 - 单一测试中完成所有检查
test.describe('综合浏览器兼容性测试', () => {
  
  test('完整兼容性测试套件', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 运行所有兼容性检查
    const compatibilityReport = await page.evaluate(() => {
      const report = {
        browser: navigator.userAgent,
        timestamp: new Date().toISOString(),
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        features: {}
      };
      
      // JavaScript ES6+ 特性检查
      const jsFeatures = {};
      
      try {
        // ES6 基础特性
        const arrow = () => true;
        jsFeatures.arrowFunctions = arrow();
        
        const name = 'test';
        jsFeatures.templateLiterals = `Hello ${name}` === 'Hello test';
        
        const [a, b] = [1, 2];
        jsFeatures.destructuring = a === 1 && b === 2;
        
        function testDefault(x = 5) { return x; }
        jsFeatures.defaultParams = testDefault() === 5;
        
        const arr = [1, 2, 3];
        const spread = [...arr];
        jsFeatures.spreadOperator = spread.length === 3;
        
        class TestClass { constructor() { this.value = 'test'; } }
        const instance = new TestClass();
        jsFeatures.classes = instance.value === 'test';
        
        let testLet = 1;
        const testConst = 2;
        jsFeatures.letConst = testLet === 1 && testConst === 2;
        
        // ES2017+ 特性
        jsFeatures.asyncAwait = (async () => true)() instanceof Promise;
        jsFeatures.objectEntries = typeof Object.entries === 'function';
        jsFeatures.objectValues = typeof Object.values === 'function';
        
        // ES2018+ 特性
        const obj = { a: 1, b: 2, c: 3 };
        const { a: aValue, ...rest } = obj;
        jsFeatures.objectRestSpread = rest.b === 2 && rest.c === 3;
        jsFeatures.asyncIteration = typeof Symbol.asyncIterator !== 'undefined';
        
        // ES2019+ 特性
        jsFeatures.arrayFlat = typeof Array.prototype.flat === 'function';
        jsFeatures.arrayFlatMap = typeof Array.prototype.flatMap === 'function';
        jsFeatures.objectFromEntries = typeof Object.fromEntries === 'function';
        
        try {
          throw new Error('test');
        } catch {
          jsFeatures.optionalCatchBinding = true;
        }
        
        // ES2020+ 特性
        const test = null ?? 'default';
        jsFeatures.nullishCoalescing = test === 'default';
        
        const objTest = { nested: { value: 'test' } };
        jsFeatures.optionalChaining = objTest?.nested?.value === 'test';
        
        jsFeatures.bigInt = typeof BigInt === 'function';
        jsFeatures.promiseSupport = typeof Promise !== 'undefined';
        jsFeatures.arrayIncludes = typeof Array.prototype.includes === 'function';
        jsFeatures.objectAssign = typeof Object.assign === 'function';
        jsFeatures.fetchAPI = typeof fetch === 'function';
        
      } catch (e) {
        jsFeatures.error = e.message;
      }
      
      report.features.javascript = jsFeatures;
      
      // CSS 特性检查
      const cssFeatures = {};
      const testElement = document.createElement('div');
      document.body.appendChild(testElement);
      
      try {
        testElement.style.display = 'flex';
        cssFeatures.flexbox = testElement.style.display === 'flex';
        
        testElement.style.display = 'grid';
        cssFeatures.cssGrid = testElement.style.display === 'grid';
        
        testElement.style.setProperty('--test-var', 'red');
        testElement.style.color = 'var(--test-var)';
        const computed = getComputedStyle(testElement);
        cssFeatures.cssVariables = computed.color === 'red' || computed.color === 'rgb(255, 0, 0)';
        
        testElement.style.transform = 'translateX(10px)';
        cssFeatures.transforms = testElement.style.transform === 'translateX(10px)';
        
        testElement.style.transition = 'all 0.3s ease';
        cssFeatures.transitions = testElement.style.transition.includes('0.3s');
        
        testElement.style.borderRadius = '5px';
        cssFeatures.borderRadius = testElement.style.borderRadius === '5px';
        
        testElement.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        cssFeatures.boxShadow = testElement.style.boxShadow !== '';
        
        cssFeatures.mediaQueries = typeof window.matchMedia === 'function';
        cssFeatures.cssOM = typeof CSS !== 'undefined';
        cssFeatures.containerQueries = CSS?.supports?.('container-type: inline-size') || false;
        cssFeatures.cssClamp = CSS?.supports?.('width: clamp(1rem, 2.5vw, 2rem)') || false;
        cssFeatures.subgrid = CSS?.supports?.('grid-template-columns: subgrid') || false;
        
      } catch (e) {
        cssFeatures.error = e.message;
      } finally {
        document.body.removeChild(testElement);
      }
      
      report.features.css = cssFeatures;
      
      // Web API 支持检查
      const webAPIs = {
        // 存储 APIs
        localStorage: typeof localStorage !== 'undefined',
        sessionStorage: typeof sessionStorage !== 'undefined',
        indexedDB: typeof indexedDB !== 'undefined',
        
        // 网络 APIs
        fetch: typeof fetch === 'function',
        xmlHttpRequest: typeof XMLHttpRequest === 'function',
        webSocket: typeof WebSocket === 'function',
        
        // DOM APIs
        querySelector: typeof document.querySelector === 'function',
        addEventListener: typeof document.addEventListener === 'function',
        getBoundingClientRect: typeof Element.prototype.getBoundingClientRect === 'function',
        
        // Observer APIs
        mutationObserver: typeof MutationObserver === 'function',
        intersectionObserver: typeof IntersectionObserver === 'function',
        resizeObserver: typeof ResizeObserver === 'function',
        performanceObserver: typeof PerformanceObserver === 'function',
        
        // 媒体 APIs
        mediaQueries: typeof window.matchMedia === 'function',
        audioContext: typeof AudioContext === 'function' || typeof window.webkitAudioContext === 'function',
        getUserMedia: typeof navigator.mediaDevices?.getUserMedia === 'function',
        
        // 设备 APIs
        geolocation: typeof navigator.geolocation === 'object',
        deviceOrientation: 'DeviceOrientationEvent' in window,
        accelerometer: 'DeviceMotionEvent' in window,
        vibration: typeof navigator.vibrate === 'function',
        
        // 新兴 APIs
        serviceWorker: 'serviceWorker' in navigator,
        webWorker: typeof Worker === 'function',
        pushNotifications: 'Notification' in window,
        paymentRequest: typeof PaymentRequest === 'function',
        webShare: typeof navigator.share === 'function',
        webAuthn: typeof navigator.credentials?.create === 'function',
        
        // Canvas/WebGL APIs
        canvas2d: (() => {
          try {
            const canvas = document.createElement('canvas');
            return !!canvas.getContext('2d');
          } catch (e) { return false; }
        })(),
        
        webgl: (() => {
          try {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
          } catch (e) { return false; }
        })(),
        
        webgl2: (() => {
          try {
            const canvas = document.createElement('canvas');
            return !!canvas.getContext('webgl2');
          } catch (e) { return false; }
        })(),
        
        // Performance APIs
        performance: typeof performance === 'object',
        performanceTiming: typeof performance.timing === 'object',
        performanceNavigation: typeof performance.navigation === 'object',
        performanceMeasure: typeof performance.measure === 'function',
        
        // CSS APIs
        cssSupports: typeof CSS?.supports === 'function',
        getComputedStyle: typeof getComputedStyle === 'function'
      };
      
      report.features.webAPIs = webAPIs;
      
      // 性能特性检查
      const performanceFeatures = {
        performanceNow: typeof performance.now === 'function',
        performanceMark: typeof performance.mark === 'function',
        performanceMeasure: typeof performance.measure === 'function',
        resourceTiming: typeof performance.getEntriesByType === 'function',
        navigationTiming: typeof performance.timing === 'object',
        userTiming: typeof performance.mark === 'function' && typeof performance.measure === 'function',
        memoryAPI: typeof performance.memory === 'object',
        highResTime: performance.timeOrigin !== undefined
      };
      
      // Paint Timing API
      try {
        const paintEntries = performance.getEntriesByType('paint');
        performanceFeatures.paintTiming = Array.isArray(paintEntries);
      } catch (e) {
        performanceFeatures.paintTiming = false;
      }
      
      // Long Task API
      try {
        const longTasks = performance.getEntriesByType('longtask');
        performanceFeatures.longTaskAPI = Array.isArray(longTasks);
      } catch (e) {
        performanceFeatures.longTaskAPI = false;
      }
      
      report.features.performance = performanceFeatures;
      
      // 安全特性检查
      const securityFeatures = {
        isHttps: location.protocol === 'https:',
        csp: typeof CSP !== 'undefined' || 
             document.querySelector('meta[http-equiv="Content-Security-Policy"]') !== null,
        referrerPolicy: 'referrerPolicy' in document,
        secureContext: typeof isSecureContext !== 'undefined' ? isSecureContext : false,
        sri: 'integrity' in document.createElement('script'),
        featurePolicy: typeof document.featurePolicy === 'object' ||
                       typeof document.permissionsPolicy === 'object',
        crossOriginIsolated: typeof crossOriginIsolated !== 'undefined' ? crossOriginIsolated : false
      };
      
      // SameSite Cookie 测试
      try {
        document.cookie = 'test=1; SameSite=Strict';
        securityFeatures.sameSiteCookies = document.cookie.includes('test=1');
        document.cookie = 'test=; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      } catch (e) {
        securityFeatures.sameSiteCookies = false;
      }
      
      report.features.security = securityFeatures;
      
      // 计算支持度
      const calculateSupport = (features) => {
        if (!features || typeof features !== 'object') return 0;
        const entries = Object.entries(features);
        const total = entries.length;
        const supported = entries.filter(([key, value]) => 
          value === true && !key.includes('Error') && !key.includes('error')
        ).length;
        return total > 0 ? Math.round((supported / total) * 100) : 0;
      };
      
      report.support = {
        javascript: calculateSupport(jsFeatures),
        css: calculateSupport(cssFeatures),
        webAPIs: calculateSupport(webAPIs),
        performance: calculateSupport(performanceFeatures),
        security: calculateSupport(securityFeatures)
      };
      
      return report;
    });
    
    // 输出测试结果
    console.log(`\n===== 综合兼容性测试报告 (${browserName}) =====`);
    console.log(`用户代理: ${compatibilityReport.browser}`);
    console.log(`视口大小: ${compatibilityReport.viewport.width}x${compatibilityReport.viewport.height}`);
    console.log(`JavaScript 支持度: ${compatibilityReport.support.javascript}%`);
    console.log(`CSS 支持度: ${compatibilityReport.support.css}%`);
    console.log(`Web APIs 支持度: ${compatibilityReport.support.webAPIs}%`);
    console.log(`性能特性支持度: ${compatibilityReport.support.performance}%`);
    console.log(`安全特性支持度: ${compatibilityReport.support.security}%`);
    console.log(`总体兼容性: ${Math.round((
      compatibilityReport.support.javascript +
      compatibilityReport.support.css +
      compatibilityReport.support.webAPIs +
      compatibilityReport.support.performance +
      compatibilityReport.support.security
    ) / 5)}%`);
    console.log(`=====================================\n`);
    
    // 详细特性支持情况
    console.log('JavaScript 特性详情:', JSON.stringify(compatibilityReport.features.javascript, null, 2));
    console.log('CSS 特性详情:', JSON.stringify(compatibilityReport.features.css, null, 2));
    console.log('Web API 详情:', JSON.stringify(compatibilityReport.features.webAPIs, null, 2));
    console.log('性能特性详情:', JSON.stringify(compatibilityReport.features.performance, null, 2));
    console.log('安全特性详情:', JSON.stringify(compatibilityReport.features.security, null, 2));
    
    // 基本兼容性要求验证
    expect(compatibilityReport.support.javascript).toBeGreaterThan(70);
    expect(compatibilityReport.support.css).toBeGreaterThan(60);
    expect(compatibilityReport.support.webAPIs).toBeGreaterThan(60);
    expect(compatibilityReport.support.performance).toBeGreaterThan(50);
    
    // 关键特性必须支持
    expect(compatibilityReport.features.javascript.promiseSupport).toBe(true);
    expect(compatibilityReport.features.javascript.fetchAPI).toBe(true);
    expect(compatibilityReport.features.javascript.arrowFunctions).toBe(true);
    expect(compatibilityReport.features.css.flexbox).toBe(true);
    expect(compatibilityReport.features.css.transforms).toBe(true);
    expect(compatibilityReport.features.webAPIs.localStorage).toBe(true);
    expect(compatibilityReport.features.webAPIs.fetch).toBe(true);
    expect(compatibilityReport.features.webAPIs.querySelector).toBe(true);
    
    // 保存详细报告到文件系统（测试结果目录）
    await page.addInitScript((report) => {
      window.compatibilityTestReport = report;
    }, compatibilityReport);
    
    return compatibilityReport;
  });
  
  // 响应式设计兼容性测试
  test('响应式设计兼容性', async ({ page, browserName }) => {
    const viewports = [
      { name: '手机', width: 375, height: 667 },
      { name: '平板', width: 768, height: 1024 },
      { name: '桌面', width: 1920, height: 1080 }
    ];
    
    const responsiveResults = {};
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
      
      const layoutInfo = await page.evaluate(() => {
        const body = document.body;
        return {
          bodyWidth: body.clientWidth,
          bodyHeight: body.clientHeight,
          scrollWidth: body.scrollWidth,
          scrollHeight: body.scrollHeight,
          hasHorizontalScroll: body.scrollWidth > body.clientWidth,
          hasVerticalScroll: body.scrollHeight > body.clientHeight
        };
      });
      
      responsiveResults[viewport.name] = layoutInfo;
      
      console.log(`${viewport.name} (${viewport.width}x${viewport.height}) 布局:`, layoutInfo);
      
      // 基本响应式要求
      expect(layoutInfo.bodyWidth).toBeLessThanOrEqual(viewport.width);
      
      // 小屏幕不应该有明显的横向滚动
      if (viewport.width <= 768) {
        expect(layoutInfo.scrollWidth - layoutInfo.bodyWidth).toBeLessThan(50);
      }
      
      // 截图
      await page.screenshot({
        path: `test-results/responsive-${viewport.name}-${browserName.replace(/\s+/g, '-')}.png`
      });
    }
    
    console.log('响应式测试结果:', responsiveResults);
  });
  
  // 交互功能兼容性测试
  test('交互功能兼容性', async ({ page, browserName }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const interactionSupport = await page.evaluate(() => {
      const support = {
        mouseEvents: true,
        keyboardEvents: true,
        touchEvents: 'ontouchstart' in window,
        dragAndDrop: typeof DragEvent !== 'undefined' && typeof DataTransfer !== 'undefined',
        clipboard: typeof navigator.clipboard !== 'undefined',
        fullscreen: typeof document.documentElement.requestFullscreen === 'function',
        orientationChange: 'orientation' in window || 'onorientationchange' in window
      };
      
      return support;
    });
    
    console.log(`交互功能支持 (${browserName}):`, interactionSupport);
    
    // 查找并测试交互元素
    const buttons = await page.locator('button, a, [role="button"]').all();
    
    if (buttons.length > 0) {
      // 测试第一个按钮的点击
      try {
        await buttons[0].click();
        console.log(`按钮点击测试通过 (${browserName})`);
      } catch (error) {
        console.log(`按钮点击测试失败 (${browserName}): ${error.message}`);
      }
    }
    
    // 测试键盘导航
    try {
      await page.keyboard.press('Tab');
      await page.keyboard.press('Enter');
      console.log(`键盘导航测试通过 (${browserName})`);
    } catch (error) {
      console.log(`键盘导航测试失败 (${browserName}): ${error.message}`);
    }
    
    // 基本交互要求
    expect(interactionSupport.mouseEvents).toBe(true);
    expect(interactionSupport.keyboardEvents).toBe(true);
  });
});