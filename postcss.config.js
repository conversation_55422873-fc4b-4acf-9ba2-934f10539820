export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    // 生产环境启用更激进的CSS优化
    ...(process.env.NODE_ENV === 'production' ? {
      'postcss-import': {},
      'postcss-nested': {},
      // 合并媒体查询
      'postcss-combine-media-query': {},
      // 优化的CSS压缩
      cssnano: {
        preset: ['default', {
          discardComments: { removeAll: true },
          normalizeWhitespace: true,
          mergeLonghand: true,
          mergeRules: true,
          minifySelectors: true,
          minifyFontValues: true,
          convertValues: { precision: 2 },
          colormin: true,
          reduceIdents: false, // 保护动画名称和自定义属性
          zindex: false, // 保护z-index值
          // 保护重要的内容
          discardUnused: { keyframes: false, fontFace: false }
        }]
      }
    } : {})
  },
}