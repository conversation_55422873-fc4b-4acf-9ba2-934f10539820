version: '3.8'

services:
  # 应用服务
  app:
    build: 
      context: .
      dockerfile: Dockerfile
      args:
        VITE_LDAP_API_URL: /api/ldap
        VITE_LITELLM_API_BASE: /api/litellm
    container_name: ynnx-ai-platform
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - TZ=Asia/Shanghai
      - LDAP_AUTH_PORT=3002
      - LDAP_DEFAULT_HOST=0.0.0.0
      - LDAP_PRIMARY_HOST=0.0.0.0
      - CORS_ORIGIN=https://localhost,http://localhost
      # LDAP 基础配置
      - LDAP_DEFAULT_ENVIRONMENT=DEVVDI_ENV
      - LDAP_CONNECTION_TIMEOUT=20
      - LDAP_SEARCH_TIMEOUT=10
      - LDAP_MAX_RETRIES=2
      # DEVVDI 环境完整配置
      - LDAP_DEVVDI_ENV_URL=ldap://**************:389
      - LDAP_DEVVDI_ENV_BASE_DN=DC=DEVVDI,DC=YNRCC,DC=COM
      - LDAP_DEVVDI_ENV_BIND_DN=
      - LDAP_DEVVDI_ENV_BIND_PASSWORD=
      - LDAP_DEVVDI_ENV_USER_SEARCH_BASE=DC=DEVVDI,DC=YNRCC,DC=COM
      - LDAP_DEVVDI_ENV_USER_FILTER=(userPrincipalName={{username}}@DEVVDI.YNRCC.COM)
      - LDAP_DEVVDI_ENV_USER_DN_PATTERN={{username}}@DEVVDI.YNRCC.COM
      - LDAP_DEVVDI_ENV_USER_DOMAIN=@DEVVDI.YNRCC.COM
      - LDAP_DEVVDI_ENV_USE_DIRECT_BIND=true
      - LDAP_DEVVDI_ENV_NAME=240.10云桌面环境
      - LDAP_DEVVDI_ENV_DESCRIPTION=DEVVDI Active Directory环境 - 端口389
      # VDI 环境完整配置
      - LDAP_VDI_ENV_URL=ldap://*************:389
      - LDAP_VDI_ENV_BASE_DN=DC=VDI,DC=YNNX,DC=COM
      - LDAP_VDI_ENV_BIND_DN=
      - LDAP_VDI_ENV_BIND_PASSWORD=
      - LDAP_VDI_ENV_USER_SEARCH_BASE=DC=VDI,DC=YNNX,DC=COM
      - LDAP_VDI_ENV_USER_FILTER=(userPrincipalName={{username}}@VDI.YNNX.COM)
      - LDAP_VDI_ENV_USER_DN_PATTERN={{username}}@VDI.YNNX.COM
      - LDAP_VDI_ENV_USER_DOMAIN=@VDI.YNNX.COM
      - LDAP_VDI_ENV_USE_DIRECT_BIND=true
      - LDAP_VDI_ENV_NAME=242.2云桌面环境
      - LDAP_VDI_ENV_DESCRIPTION=VDI Active Directory环境 - 端口389
      # LLM 配置
      - OPENAI_BASE_URL=https://***************:4000/v1
      - OPENAI_MODEL=qwen3-235b-a22b
      - OPENAI_API_KEY=sk-ynnx-llm-20250530
      - ENABLE_OPENAI=true
      - LITELLM_MASTER_KEY=sk-ynnx-llm-20250530
    volumes:
      - ./logs:/var/log/ynnx-ai
      - ./dist:/app/dist
    ports:
      - "3002:3002"
    networks:
      - ynnx-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ynnx-nginx
    restart: unless-stopped
    ports:
      - "80:80"        # HTTP (重定向到HTTPS)
      - "443:443"      # HTTPS
      - "8443:8443"    # WebSocket专用端口 (HTTP/1.1)
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./dist:/usr/share/nginx/html:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - ynnx-network
    depends_on:
      app:
        condition: service_started
      openvscode:
        condition: service_healthy
      lobe-chat:
        condition: service_healthy

  # OpenVSCode 服务
  openvscode:
    image: gitpod/openvscode-server
    container_name: ynnx-openvscode
    restart: unless-stopped
    privileged: true
    security_opt:
      - seccomp:unconfined
    ports:
      - "3667:3000"
    environment:
      # WebSocket连接配置 - 使用HTTP而非HTTPS避免SSL证书问题
      - VSCODE_PROXY_URI=http://132.147.223.250:8443
      - CONNECTION_TOKEN=tk-ynnx-llm
      # 禁用一些可能引起问题的功能
      - DISABLE_UPDATE_CHECK=true
      - DISABLE_TELEMETRY=true
      # 内网环境配置 - 禁用外部资源访问
      - VSCODE_SERVE_STATIC=true
      - VSCODE_DISABLE_WEB_EXTENSIONS=false
      - OPENVSCODE_SERVER_ROOT=/home/<USER>
      # CDN和外部资源配置
      - DISABLE_WEB_DOWNLOADS=true
      - EXTENSIONS_AUTO_UPDATE=false
      - DISABLE_UPDATE_NOTIFICATIONS=true
      # Node.js性能优化配置
      - NODE_OPTIONS=--max-old-space-size=2048
      - UV_THREADPOOL_SIZE=128
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    volumes:
      - ./.openvscode:/home/<USER>
      - ./openvscode-extensions:/home/<USER>/extensions
      - ./openvscode-data:/home/<USER>/data
      - ./openvscode-cache:/home/<USER>
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    networks:
      - ynnx-network

  # LobeChat 服务
  lobe-chat:
    image: lobehub/lobe-chat
    container_name: ynnx-lobe-chat
    restart: unless-stopped
    privileged: true
    security_opt:
      - seccomp:unconfined
    ports:
      - "3210:3210"
    environment:
      - ACCESS_CODE=lobe66
      # Node.js性能优化配置
      - NODE_OPTIONS=--max-old-space-size=1024
      - UV_THREADPOOL_SIZE=64
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3210/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    networks:
      - ynnx-network


# ===================================================================
# 服务说明
# ===================================================================
#
# LobeChat 智能对话服务
# - 服务地址: http://localhost:3210
# - 代理路径: /chat/
# - 认证方式: 通过 nginx 代理传递平台认证信息
# - 文档: docs/lobechat-integration.md
#
# OpenVSCode 代码编辑器服务
# - 服务地址: http://localhost:3667
# - 代理路径: /ide/
# - 认证方式: 通过 nginx 代理传递平台认证信息
# ===================================================================

networks:
  ynnx-network:
    driver: bridge