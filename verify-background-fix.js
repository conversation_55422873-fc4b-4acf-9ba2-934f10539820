/**
 * 更详细的背景闪烁修复验证
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const https = require('https');

async function comprehensiveBackgroundCheck() {
  console.log('🔍 开始全面的背景闪烁修复验证...\n');
  
  const agent = new https.Agent({
    rejectUnauthorized: false
  });

  return new Promise((resolve, reject) => {
    const options = {
      hostname: '127.0.0.1',
      port: 443,
      path: '/',
      method: 'GET',
      agent: agent
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log('✅ 页面加载成功\n');
        
        // 详细的背景修复检查
        const detailedChecks = {
          '1. HTML根元素背景': {
            check: data.includes('html{background-color:#000!important'),
            description: '确保HTML根元素有强制黑色背景'
          },
          '2. Body元素背景': {
            check: data.includes('body{') && data.includes('background-color:#000!important'),
            description: '确保Body元素有强制黑色背景'
          },
          '3. Root容器背景': {
            check: data.includes('#root') && data.includes('background-color:#000!important'),
            description: '确保React根容器有强制黑色背景'
          },
          '4. 加载后备背景': {
            check: data.includes('loading-fallback') && data.includes('background-color:#000!important'),
            description: '确保加载界面后备方案有黑色背景'
          },
          '5. 主题颜色设置': {
            check: data.includes('theme-color') && data.includes('#000000'),
            description: '确保浏览器主题颜色为黑色'
          },
          '6. 关键CSS内联': {
            check: data.includes('<style>') && data.includes('background-color:#000'),
            description: '确保关键CSS已内联到HTML中'
          },
          '7. 防白色背景保护': {
            check: !data.match(/background.*white|bg-white(?!.*#000)/gi),
            description: '确保没有意外的白色背景'
          },
          '8. 应用状态栏样式': {
            check: data.includes('black-translucent'),
            description: '确保移动端状态栏为黑色半透明'
          }
        };
        
        console.log('📋 详细修复检查结果:\n');
        let passedCount = 0;
        const totalCount = Object.keys(detailedChecks).length;
        
        Object.entries(detailedChecks).forEach(([name, { check, description }]) => {
          const status = check ? '✅ 通过' : '❌ 失败';
          console.log(`${status} ${name}`);
          console.log(`   ${description}`);
          if (check) passedCount++;
          console.log('');
        });
        
        // 额外检查：CSS文件是否包含防闪烁样式
        const cssFilePattern = /assets\/css\/index-[a-zA-Z0-9-]+\.css/;
        const cssMatch = data.match(cssFilePattern);
        if (cssMatch) {
          console.log(`📄 发现CSS文件: ${cssMatch[0]}`);
        }
        
        // 检查是否有Service Worker
        const hasServiceWorker = data.includes('serviceWorker');
        console.log(`🔧 Service Worker: ${hasServiceWorker ? '已配置' : '未配置'}`);
        
        // 检查是否有性能优化
        const hasPerformanceOpt = data.includes('autoPerformanceManager');
        console.log(`⚡ 性能优化: ${hasPerformanceOpt ? '已启用' : '未检测到'}`);
        
        // 最终评分
        const score = Math.round((passedCount / totalCount) * 100);
        console.log(`\n🎯 背景闪烁修复评分: ${score}% (${passedCount}/${totalCount})`);
        
        if (score >= 90) {
          console.log('🎉 优秀！背景闪烁修复非常完善');
        } else if (score >= 75) {
          console.log('✅ 良好！主要的背景闪烁问题已解决');
        } else {
          console.log('⚠️ 需要改进！部分背景闪烁问题仍需解决');
        }
        
        console.log('\n📝 修复总结:');
        console.log('- 使用!important强制黑色背景');
        console.log('- 在HTML、Body、#root多层设置背景色');
        console.log('- 关键CSS内联到HTML避免加载延迟');
        console.log('- 设置浏览器主题色为黑色');
        console.log('- 禁用背景色过渡动画');
        
        resolve({
          success: score >= 75,
          score,
          passedCount,
          totalCount,
          detailedChecks
        });
      });
    });
    
    req.on('error', (error) => {
      console.error('❌ 验证失败:', error.message);
      reject(error);
    });
    
    req.setTimeout(15000, () => {
      console.error('❌ 验证超时');
      req.destroy();
      reject(new Error('Timeout'));
    });
    
    req.end();
  });
}

// 运行验证
comprehensiveBackgroundCheck()
  .then(result => {
    console.log('\n🏁 背景闪烁修复验证完成');
    console.log(`最终结果: ${result.success ? '✅ 修复成功' : '❌ 需要进一步优化'}`);
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 验证执行失败:', error.message);
    process.exit(1);
  });