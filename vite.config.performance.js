import { defineConfig, devices } from '@playwright/test';
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'

// 性能优化版本的构建配置
export default defineConfig({
  // 为 Node.js 模块提供浏览器兼容的空实现
  define: {
    global: 'globalThis',
    'process.env.NODE_ENV': '"production"',
    // 内网部署：确保不会尝试访问外部资源
    __INTRANET_MODE__: true,
    __DISABLE_EXTERNAL_RESOURCES__: true,
    __OFFLINE_MODE__: true,
    __PERFORMANCE_MODE__: true, // 性能模式标志
    // 禁用segment analytics在浏览器环境中的使用
    'process.env.SEGMENT_ANALYTICS_DISABLED': '"true"',
    // React 兼容性定义
    '__DEV__': 'false'
  },
  
  // 解析配置 - 排除Node.js专用包
  resolve: {
    alias: {
      // 为Node.js专用包提供空实现
      '@segment/analytics-node': 'virtual:segment-mock',
      'node-fetch': false,
    }
  },

  plugins: [
    react(),
    visualizer({ 
      open: false, 
      filename: 'logs/performance-stats.html',
      gzipSize: true,
      brotliSize: true
    }),
    // 性能优化插件
    {
      name: 'performance-optimizer',
      generateBundle(options, bundle) {
        // 移除大的非必要依赖
        for (const [fileName, chunk] of Object.entries(bundle)) {
          if (chunk.type === 'chunk' && chunk.code) {
            // 移除不必要的console.log (除了错误日志)
            chunk.code = chunk.code
              .replace(/console\.(log|info|debug)\([^)]*\);?/g, '')
              .replace(/console\.warn\(/g, 'console.error('); // 将警告降级为错误
          }
        }
      }
    }
  ],

  server: {
    host: '0.0.0.0',
    port: 5173,
    strictPort: false,
    open: false
  },

  build: {
    // 针对低端设备优化
    target: ['es2015', 'chrome55'],
    minify: 'terser',
    
    terserOptions: {
      compress: {
        // 更激进的压缩
        drop_console: ['log', 'info', 'debug'], // 保留 warn 和 error
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        // 移除未使用的代码
        dead_code: true,
        // 优化条件表达式
        conditionals: true,
        // 优化比较操作
        comparisons: true,
        // 合并变量
        join_vars: true,
        // 移除无用的分号
        semicolons: true,
        // 优化 if-else 结构
        if_return: true,
        // 内联函数
        inline: 2,
        // 减少变量作用域
        reduce_vars: true
      },
      mangle: {
        // 混淆变量名以减小大小
        toplevel: true,
        safari10: true
      }
    },
    
    cssMinify: 'esbuild',
    modulePreload: false,
    sourcemap: false,
    reportCompressedSize: process.env.BUILD_VERBOSE === 'true',
    
    // 更小的chunk大小限制
    chunkSizeWarningLimit: 800, // 降低到800KB
    assetsInlineLimit: 2048, // 降低到2KB
    
    rollupOptions: {
      // 外部化更多依赖以减小bundle大小
      external: [
        '@segment/analytics-node', 
        'node-fetch',
        // 在性能模式下外部化大的可选依赖
        /^highlight\.js\/lib\/languages\//,
        /^react-icons\/(?!fa|hi)/  // 只保留常用图标包
      ],

      output: {
        // 更激进的代码分割策略
        manualChunks: (id) => {
          // 核心React包 - 单独分割
          if (id.includes('react-dom')) return 'vendor-react-dom';
          if (id.includes('react/') || id.includes('scheduler')) return 'vendor-react';
          
          // 将所有第三方库合并到一个chunk以减少HTTP请求
          if (id.includes('node_modules')) {
            // 大的库单独分割
            if (id.includes('highlight.js')) return 'vendor-highlight';
            if (id.includes('react-markdown') || id.includes('remark') || 
                id.includes('rehype') || id.includes('unified') || 
                id.includes('micromark')) return 'vendor-markdown';
            
            // 其他所有第三方库合并
            return 'vendor';
          }

          // 应用代码合并到更少的chunks
          if (id.includes('src/components')) return 'components';
          if (id.includes('src/services')) return 'services';
          if (id.includes('src/utils')) return 'utils';
        },
        
        // 优化文件命名
        chunkFileNames: 'js/[name]-[hash:8].js',
        entryFileNames: 'js/[name]-[hash:8].js',
        
        assetFileNames: (assetInfo) => {
          const ext = assetInfo.name.split('.').pop();
          return `${ext}/[name]-[hash:8][extname]`;
        }
      }
    }
  },
  
  // 更激进的依赖优化
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      // 只预构建必需的库
      'axios'
    ],
    exclude: [
      // 排除所有非必需依赖
      'openai',
      '@anthropic-ai/sdk',
      'express',
      'cors',
      'ldapjs',
      'node-fetch',
      'dotenv',
      'uuid',
      'concurrently',
      'react-router-dom',
      '@segment/analytics-node',
      '@segment/analytics-next',
      // 延迟加载的图标
      'react-icons',
      // 按需加载的语法高亮
      'highlight.js',
      'prismjs'
    ]
  },
  
  // CSS优化
  css: {
    devSourcemap: false,
    preprocessorOptions: {
      scss: {
        charset: false
      }
    }
  },

  // 性能优化
  esbuild: {
    target: 'es2015',
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
    legalComments: 'none',
    // 更小的标识符
    minifyIdentifiers: true,
    minifySyntax: true,
    minifyWhitespace: true
  }
});