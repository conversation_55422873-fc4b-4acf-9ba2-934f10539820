{"config": {"configFile": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/playwright.config.js", "rootDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/compatibility-report.json"}], ["junit", {"outputFile": "test-results/compatibility-junit.xml"}], ["list", null], ["github", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Chrome 50", "name": "Chrome 50", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Chrome 60", "name": "Chrome 60", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Chrome 70", "name": "Chrome 70", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Chrome 80", "name": "Chrome 80", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Chrome 90", "name": "Chrome 90", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Chrome 100", "name": "Chrome 100", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Chrome Latest", "name": "Chrome Latest", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Firefox 52", "name": "Firefox 52", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Firefox 60 ESR", "name": "Firefox 60 ESR", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Firefox 78 ESR", "name": "Firefox 78 ESR", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Firefox 100", "name": "Firefox 100", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Firefox Latest", "name": "Firefox Latest", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Safari 10", "name": "Safari 10", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Safari 14", "name": "Safari 14", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Safari Latest", "name": "Safari Latest", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Edge 15", "name": "Edge 15", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Edge 79 (Chromium)", "name": "Edge 79 (Chromium)", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Edge Latest", "name": "Edge Latest", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "1366x768 (Common Laptop)", "name": "1366x768 (Common Laptop)", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "1920x1080 (Full HD)", "name": "1920x1080 (Full HD)", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "2560x1440 (2K)", "name": "2560x1440 (2K)", "testDir": "/Users/<USER>/Workspaces/ynnx-aidev-platform/ynnx-aidev-platform/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 1, "webServer": {"command": "npm run dev", "port": 5173, "reuseExistingServer": true, "timeout": 60000}}, "suites": [{"title": "chrome-versions-compatibility.spec.js", "file": "chrome-versions-compatibility.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Chrome 关键版本兼容性测试", "file": "chrome-versions-compatibility.spec.js", "line": 4, "column": 6, "specs": [{"title": "Chrome 50 详细兼容性分析", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Chrome 90", "projectName": "Chrome 90", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1515, "errors": [], "stdout": [{"text": "\n========== Chrome 50 兼容性分析 ==========\n"}, {"text": "发布时间: 2016年4月\n"}, {"text": "期望版本: 50.0\n"}, {"text": "用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36\n"}, {"text": "关键特性: Proxies, Spread operator, Rest parameters, destructuring\n"}, {"text": "-------------------\n"}, {"text": "JavaScript 支持度: 93%\n"}, {"text": "CSS 支持度: 75%\n"}, {"text": "Web APIs 支持度: 100%\n"}, {"text": "性能 APIs 支持度: 100%\n"}, {"text": "总体支持度: 92%\n"}, {"text": "=======================================\n\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-04T17:08:54.423Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ef27271abf94ee7beb0c-992dc9c76a7283ca15bd", "file": "chrome-versions-compatibility.spec.js", "line": 78, "column": 5}, {"title": "Chrome 60 详细兼容性分析", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Chrome 90", "projectName": "Chrome 90", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 877, "errors": [], "stdout": [{"text": "\n========== Chrome 60 兼容性分析 ==========\n"}, {"text": "发布时间: 2017年7月\n"}, {"text": "期望版本: 60.0\n"}, {"text": "用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36\n"}, {"text": "关键特性: ES2017 (async/await), CSS Grid Layout, Payment Request API\n"}, {"text": "-------------------\n"}, {"text": "JavaScript 支持度: 93%\n"}, {"text": "CSS 支持度: 75%\n"}, {"text": "Web APIs 支持度: 100%\n"}, {"text": "性能 APIs 支持度: 100%\n"}, {"text": "总体支持度: 92%\n"}, {"text": "=======================================\n\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-04T17:08:56.247Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ef27271abf94ee7beb0c-c8d30e0c8a5b3c54fa1b", "file": "chrome-versions-compatibility.spec.js", "line": 78, "column": 5}, {"title": "Chrome 70 详细兼容性分析", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Chrome 90", "projectName": "Chrome 90", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 816, "errors": [], "stdout": [{"text": "\n========== Chrome 70 兼容性分析 ==========\n"}, {"text": "发布时间: 2018年10月\n"}, {"text": "期望版本: 70.0\n"}, {"text": "用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36\n"}, {"text": "关键特性: CSS Grid完全支持, Web Authentication API, BigInt\n"}, {"text": "-------------------\n"}, {"text": "JavaScript 支持度: 93%\n"}, {"text": "CSS 支持度: 75%\n"}, {"text": "Web APIs 支持度: 100%\n"}, {"text": "性能 APIs 支持度: 100%\n"}, {"text": "总体支持度: 92%\n"}, {"text": "=======================================\n\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-04T17:08:57.126Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ef27271abf94ee7beb0c-fa5bbcec501e421eb983", "file": "chrome-versions-compatibility.spec.js", "line": 78, "column": 5}, {"title": "Chrome 80 详细兼容性分析", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Chrome 90", "projectName": "Chrome 90", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 815, "errors": [], "stdout": [{"text": "\n========== Chrome 80 兼容性分析 ==========\n"}, {"text": "发布时间: 2020年2月\n"}, {"text": "期望版本: 80.0\n"}, {"text": "用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36\n"}, {"text": "关键特性: Optional Chaining (?.), Nullish Coalescing (??), ES2020\n"}, {"text": "-------------------\n"}, {"text": "JavaScript 支持度: 93%\n"}, {"text": "CSS 支持度: 75%\n"}, {"text": "Web APIs 支持度: 100%\n"}, {"text": "性能 APIs 支持度: 100%\n"}, {"text": "总体支持度: 92%\n"}, {"text": "=======================================\n\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-04T17:08:57.944Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ef27271abf94ee7beb0c-df726c2183b67311d8b3", "file": "chrome-versions-compatibility.spec.js", "line": 78, "column": 5}, {"title": "Chrome 90 详细兼容性分析", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Chrome 90", "projectName": "Chrome 90", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 845, "errors": [], "stdout": [{"text": "\n========== Chrome 90 兼容性分析 ==========\n"}, {"text": "发布时间: 2021年4月\n"}, {"text": "期望版本: 90.0\n"}, {"text": "用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36\n"}, {"text": "关键特性: ES2021, Logical Assignment, CSS aspect-ratio, CSS :is()\n"}, {"text": "-------------------\n"}, {"text": "JavaScript 支持度: 93%\n"}, {"text": "CSS 支持度: 75%\n"}, {"text": "Web APIs 支持度: 100%\n"}, {"text": "性能 APIs 支持度: 100%\n"}, {"text": "总体支持度: 92%\n"}, {"text": "=======================================\n\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-04T17:08:58.761Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ef27271abf94ee7beb0c-e3878156179595bf720a", "file": "chrome-versions-compatibility.spec.js", "line": 78, "column": 5}, {"title": "Chrome 100 详细兼容性分析", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Chrome 90", "projectName": "Chrome 90", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 818, "errors": [], "stdout": [{"text": "\n========== Chrome 100 兼容性分析 ==========\n"}, {"text": "发布时间: 2022年3月\n"}, {"text": "期望版本: 100.0\n"}, {"text": "用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36\n"}, {"text": "关键特性: ES2022, Container Queries (experimental), CSS @layer\n"}, {"text": "-------------------\n"}, {"text": "JavaScript 支持度: 93%\n"}, {"text": "CSS 支持度: 75%\n"}, {"text": "Web APIs 支持度: 100%\n"}, {"text": "性能 APIs 支持度: 100%\n"}, {"text": "总体支持度: 92%\n"}, {"text": "=======================================\n\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-04T17:08:59.607Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ef27271abf94ee7beb0c-2425f0e681b8ab64edba", "file": "chrome-versions-compatibility.spec.js", "line": 78, "column": 5}, {"title": "Chrome 版本演进对比分析", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Chrome 90", "projectName": "Chrome 90", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 847, "errors": [], "stdout": [{"text": "\\n========== Chrome 版本演进分析 ==========\n"}, {"text": "当前浏览器: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36\n"}, {"text": "检测到的版本: 90.0.4430.212\n"}, {"text": "特性支持演进:\n"}, {"text": "  Chrome 50+ ES6 Proxies: ✅ 支持\n"}, {"text": "  Chrome 60+ ES2017 async/await: ✅ 支持\n"}, {"text": "  Chrome 60+ CSS Grid: ✅ 支持\n"}, {"text": "  Chrome 70+ BigInt: ✅ 支持\n"}, {"text": "  Chrome 80+ Optional Chaining: ✅ 支持\n"}, {"text": "  Chrome 80+ Nullish Coalescing: ✅ 支持\n"}, {"text": "  Chrome 90+ CSS aspect-ratio: ✅ 支持\n"}, {"text": "  Chrome 100+ Class Fields: ✅ 支持\n"}, {"text": "  Chrome 100+ Container Queries: ✅ 支持\n"}, {"text": "=======================================\\n\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-04T17:09:00.427Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ef27271abf94ee7beb0c-a96b81917c9f522980ff", "file": "chrome-versions-compatibility.spec.js", "line": 442, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-09-04T17:08:53.841Z", "duration": 7519.166, "expected": 7, "skipped": 0, "unexpected": 0, "flaky": 0}}