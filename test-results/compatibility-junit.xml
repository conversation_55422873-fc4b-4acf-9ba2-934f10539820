<testsuites id="" name="" tests="7" failures="0" skipped="0" errors="0" time="7.519166">
<testsuite name="chrome-versions-compatibility.spec.js" timestamp="2025-09-04T17:08:54.212Z" hostname="Chrome 90" tests="7" failures="0" skipped="0" time="6.533" errors="0">
<testcase name="Chrome 关键版本兼容性测试 › Chrome 50 详细兼容性分析" classname="chrome-versions-compatibility.spec.js" time="1.515">
<system-out>
<![CDATA[
========== Chrome 50 兼容性分析 ==========
发布时间: 2016年4月
期望版本: 50.0
用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36
关键特性: Proxies, Spread operator, Rest parameters, destructuring
-------------------
JavaScript 支持度: 93%
CSS 支持度: 75%
Web APIs 支持度: 100%
性能 APIs 支持度: 100%
总体支持度: 92%
=======================================

]]>
</system-out>
</testcase>
<testcase name="Chrome 关键版本兼容性测试 › Chrome 60 详细兼容性分析" classname="chrome-versions-compatibility.spec.js" time="0.877">
<system-out>
<![CDATA[
========== Chrome 60 兼容性分析 ==========
发布时间: 2017年7月
期望版本: 60.0
用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36
关键特性: ES2017 (async/await), CSS Grid Layout, Payment Request API
-------------------
JavaScript 支持度: 93%
CSS 支持度: 75%
Web APIs 支持度: 100%
性能 APIs 支持度: 100%
总体支持度: 92%
=======================================

]]>
</system-out>
</testcase>
<testcase name="Chrome 关键版本兼容性测试 › Chrome 70 详细兼容性分析" classname="chrome-versions-compatibility.spec.js" time="0.816">
<system-out>
<![CDATA[
========== Chrome 70 兼容性分析 ==========
发布时间: 2018年10月
期望版本: 70.0
用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36
关键特性: CSS Grid完全支持, Web Authentication API, BigInt
-------------------
JavaScript 支持度: 93%
CSS 支持度: 75%
Web APIs 支持度: 100%
性能 APIs 支持度: 100%
总体支持度: 92%
=======================================

]]>
</system-out>
</testcase>
<testcase name="Chrome 关键版本兼容性测试 › Chrome 80 详细兼容性分析" classname="chrome-versions-compatibility.spec.js" time="0.815">
<system-out>
<![CDATA[
========== Chrome 80 兼容性分析 ==========
发布时间: 2020年2月
期望版本: 80.0
用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36
关键特性: Optional Chaining (?.), Nullish Coalescing (??), ES2020
-------------------
JavaScript 支持度: 93%
CSS 支持度: 75%
Web APIs 支持度: 100%
性能 APIs 支持度: 100%
总体支持度: 92%
=======================================

]]>
</system-out>
</testcase>
<testcase name="Chrome 关键版本兼容性测试 › Chrome 90 详细兼容性分析" classname="chrome-versions-compatibility.spec.js" time="0.845">
<system-out>
<![CDATA[
========== Chrome 90 兼容性分析 ==========
发布时间: 2021年4月
期望版本: 90.0
用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36
关键特性: ES2021, Logical Assignment, CSS aspect-ratio, CSS :is()
-------------------
JavaScript 支持度: 93%
CSS 支持度: 75%
Web APIs 支持度: 100%
性能 APIs 支持度: 100%
总体支持度: 92%
=======================================

]]>
</system-out>
</testcase>
<testcase name="Chrome 关键版本兼容性测试 › Chrome 100 详细兼容性分析" classname="chrome-versions-compatibility.spec.js" time="0.818">
<system-out>
<![CDATA[
========== Chrome 100 兼容性分析 ==========
发布时间: 2022年3月
期望版本: 100.0
用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36
关键特性: ES2022, Container Queries (experimental), CSS @layer
-------------------
JavaScript 支持度: 93%
CSS 支持度: 75%
Web APIs 支持度: 100%
性能 APIs 支持度: 100%
总体支持度: 92%
=======================================

]]>
</system-out>
</testcase>
<testcase name="Chrome 关键版本兼容性测试 › Chrome 版本演进对比分析" classname="chrome-versions-compatibility.spec.js" time="0.847">
<system-out>
<![CDATA[\n========== Chrome 版本演进分析 ==========
当前浏览器: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36
检测到的版本: 90.0.4430.212
特性支持演进:
  Chrome 50+ ES6 Proxies: ✅ 支持
  Chrome 60+ ES2017 async/await: ✅ 支持
  Chrome 60+ CSS Grid: ✅ 支持
  Chrome 70+ BigInt: ✅ 支持
  Chrome 80+ Optional Chaining: ✅ 支持
  Chrome 80+ Nullish Coalescing: ✅ 支持
  Chrome 90+ CSS aspect-ratio: ✅ 支持
  Chrome 100+ Class Fields: ✅ 支持
  Chrome 100+ Container Queries: ✅ 支持
=======================================\n
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>