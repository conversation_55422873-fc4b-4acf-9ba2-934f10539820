#!/bin/bash
# ===================================================================
# YNNX AI Platform - 简化版离线部署打包脚本
# 
# 功能：打包整个项目目录，排除 .git 目录
# ===================================================================

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
PACKAGE_NAME="ynnx-ai-platform-simple-${TIMESTAMP}"

echo "YNNX AI Platform 简化版打包脚本"
echo "================================="
echo "开始时间: $(date)"
echo "项目目录: $PROJECT_ROOT"
echo

# 检查必要条件
echo "1. 检查先决条件..."
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: docker-compose未安装"
    exit 1
fi

if [[ ! -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
    echo "错误: docker-compose.yml文件不存在"
    exit 1
fi

echo "✓ 先决条件检查通过"

# 构建前端应用
echo "2. 构建前端应用..."
cd "$PROJECT_ROOT"

if [[ ! -d "node_modules" ]]; then
    npm install
fi

npm run build

if [[ ! -d "dist" ]]; then
    echo "错误: 构建失败，dist目录不存在"
    exit 1
fi

echo "✓ 前端应用构建完成"

# 构建并保存Docker镜像
echo "3. 构建和保存Docker镜像..."
docker-compose build

# 创建临时目录保存Docker镜像
TEMP_IMAGES_DIR="$PROJECT_ROOT/.docker-images-temp"
mkdir -p "$TEMP_IMAGES_DIR"

echo "  保存Docker镜像到临时目录..."
# 获取所有需要的镜像
required_images=("ynnx-aidev-platform-app:latest" "nginx:alpine" "gitpod/openvscode-server:latest" "lobehub/lobe-chat:latest")

for image in "${required_images[@]}"; do
    if docker image inspect "$image" &> /dev/null; then
        image_file="${image//\//_}"
        image_file="${image_file//:/_}.tar"
        echo "    保存镜像: $image"
        docker save "$image" -o "$TEMP_IMAGES_DIR/$image_file"
    else
        echo "    警告: 镜像不存在，跳过: $image"
    fi
done

echo "✓ Docker镜像保存完成"

# 生成版本信息
echo "4. 生成版本信息..."
git_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
git_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")

cat > "$PROJECT_ROOT/DEPLOYMENT_INFO.txt" << EOF
YNNX AI Platform 简化版部署包
============================

构建时间: $(date)
Git提交: $git_commit  
Git分支: $git_branch
包名称: $PACKAGE_NAME

包含内容:
- 完整项目源代码和配置
- 构建好的前端应用 (dist/)
- Docker镜像文件 (.docker-images-temp/)
- 所有配置文件和脚本

部署方法:
1. 解压部署包: tar -xzf ${PACKAGE_NAME}.tar.gz
2. 进入项目目录: cd $(basename "$PROJECT_ROOT")
3. 加载Docker镜像: 
   for img in .docker-images-temp/*.tar; do docker load -i "\$img"; done
4. 启动服务: docker-compose up -d

注意事项:
- 确保目标服务器已安装 Docker 和 docker-compose
- 根据实际网络环境修改配置文件中的IP地址
- 首次启动可能需要几分钟时间等待所有服务就绪
EOF

# 创建部署脚本
echo "5. 创建部署脚本..."
cat > "$PROJECT_ROOT/deploy-simple.sh" << 'EOF'
#!/bin/bash
# YNNX AI Platform 简化版部署脚本

set -euo pipefail

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOCKER_IMAGES_DIR="$PROJECT_DIR/.docker-images-temp"

echo "YNNX AI Platform 简化版部署脚本"
echo "================================"
echo "开始时间: $(date)"
echo "项目目录: $PROJECT_DIR"

# 检查Docker
echo "1. 检查Docker环境..."
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: docker-compose未安装"
    exit 1
fi

echo "✓ Docker环境检查通过"

# 加载Docker镜像
echo "2. 加载Docker镜像..."
if [[ -d "$DOCKER_IMAGES_DIR" ]]; then
    for image_file in "$DOCKER_IMAGES_DIR"/*.tar; do
        if [[ -f "$image_file" ]]; then
            echo "  加载镜像: $(basename "$image_file")"
            docker load -i "$image_file"
        fi
    done
    echo "✓ Docker镜像加载完成"
else
    echo "⚠️  警告: Docker镜像目录不存在，跳过镜像加载"
fi

# 创建必要目录
echo "3. 准备运行环境..."
cd "$PROJECT_DIR"
mkdir -p logs/{app,nginx}
mkdir -p openvscode-extensions openvscode-data openvscode-cache .openvscode

# 设置权限（如果是Linux环境）
if [[ "$OSTYPE" != "darwin"* ]]; then
    chown -R 1000:1000 openvscode-* .openvscode 2>/dev/null || true
fi

echo "✓ 运行环境准备完成"

echo
echo "======================================="
echo "部署准备完成！"
echo "======================================="
echo
echo "请执行以下命令启动服务:"
echo "  docker-compose up -d"
echo
echo "服务启动后可通过以下地址访问:"
local_ip=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "localhost")
echo "  主页面: https://$local_ip"
echo "  VSCode IDE: https://$local_ip/ide/"
echo "  LobeChat: https://$local_ip/chat/"
echo
echo "停止服务:"
echo "  docker-compose down"
echo
echo "查看服务状态:"
echo "  docker-compose ps"
echo "  docker-compose logs"
echo
echo "部署完成时间: $(date)"
EOF

chmod +x "$PROJECT_ROOT/deploy-simple.sh"

# 创建压缩包
echo "6. 创建压缩包..."
cd "$(dirname "$PROJECT_ROOT")"

# macOS 系统需要特殊处理扩展属性问题
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "  检测到 macOS 系统，使用兼容 Linux 的打包方式..."
    
    # 使用多种方法确保不包含扩展属性
    export COPYFILE_DISABLE=1
    export TAR_OPTIONS="--no-mac-metadata"
    
    # 使用 tar 打包，排除 .git 目录
    echo "  打包项目目录（排除 .git）..."
    tar -czf "${PACKAGE_NAME}.tar.gz" \
        --exclude="$(basename "$PROJECT_ROOT")/.git" \
        --exclude="$(basename "$PROJECT_ROOT")/node_modules" \
        --exclude="$(basename "$PROJECT_ROOT")/deployment-package" \
        --exclude="$(basename "$PROJECT_ROOT")/test-results" \
        --exclude="$(basename "$PROJECT_ROOT")/playwright-report" \
        --exclude="$(basename "$PROJECT_ROOT")/ynnx-*.tar.gz*" \
        --exclude="$(basename "$PROJECT_ROOT")/ynnx-production-*" \
        "$(basename "$PROJECT_ROOT")"
else
    # Linux 系统，使用标准方式
    echo "  使用标准方式打包..."
    tar -czf "${PACKAGE_NAME}.tar.gz" \
        --exclude="$(basename "$PROJECT_ROOT")/.git" \
        --exclude="$(basename "$PROJECT_ROOT")/node_modules" \
        --exclude="$(basename "$PROJECT_ROOT")/deployment-package" \
        --exclude="$(basename "$PROJECT_ROOT")/test-results" \
        --exclude="$(basename "$PROJECT_ROOT")/playwright-report" \
        --exclude="$(basename "$PROJECT_ROOT")/ynnx-*.tar.gz*" \
        --exclude="$(basename "$PROJECT_ROOT")/ynnx-production-*" \
        "$(basename "$PROJECT_ROOT")"
fi

# 生成校验和
sha256sum "${PACKAGE_NAME}.tar.gz" > "${PACKAGE_NAME}.tar.gz.sha256"

# 移动到项目目录
mv "${PACKAGE_NAME}.tar.gz" "$PROJECT_ROOT/"
mv "${PACKAGE_NAME}.tar.gz.sha256" "$PROJECT_ROOT/"

# 显示结果
cd "$PROJECT_ROOT"
file_size=$(du -h "${PACKAGE_NAME}.tar.gz" | cut -f1)
checksum=$(cut -d' ' -f1 "${PACKAGE_NAME}.tar.gz.sha256")

echo
echo "======================================="
echo "打包完成！"
echo "======================================="
echo "部署包: ${PACKAGE_NAME}.tar.gz ($file_size)"
echo "校验和: ${PACKAGE_NAME}.tar.gz.sha256"  
echo "SHA256: $checksum"
echo
echo "部署步骤:"
echo "1. 上传 ${PACKAGE_NAME}.tar.gz 到目标服务器"
echo "2. 解压: tar -xzf ${PACKAGE_NAME}.tar.gz"
echo "3. 进入目录: cd $(basename "$PROJECT_ROOT")"
echo "4. 运行部署: ./deploy-simple.sh"
echo "5. 启动服务: docker-compose up -d"
echo
echo "完成时间: $(date)"

# 清理临时文件
echo
echo "7. 清理临时文件..."
rm -rf "$TEMP_IMAGES_DIR"
rm -f "$PROJECT_ROOT/DEPLOYMENT_INFO.txt"
echo "✓ 临时文件清理完成"