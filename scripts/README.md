# Scripts 目录说明

本目录包含用于项目构建、部署和维护的各种脚本工具。

## 🚀 核心脚本

### `simple-package.sh`
**简化版打包脚本** - 推荐使用
- 直接打包整个项目目录
- 包含所有 Docker 镜像和配置
- 解决了 macOS 扩展属性兼容性问题
- 用法：`./scripts/simple-package.sh`

### `start-ldap.js` 
**LDAP 认证服务启动器**
- 启动 LDAP 认证 API 服务
- 被 package.json 中的 `npm run ldap` 使用
- 支持多环境配置

### `optimize-critical-path.js`
**关键路径优化**
- 优化前端构建的关键资源加载
- 自动在 `npm run build` 中执行

### `replace-external-links.cjs`
**外部链接替换**
- 替换构建产物中的外部链接为本地资源
- 自动在构建流程中执行

## 🏭 内网部署脚本

### `build-intranet.cjs`
内网环境构建脚本
- 用法：`npm run build:intranet`

### `prepare-intranet-deployment.cjs`
内网部署准备
- 用法：`npm run intranet:prepare`

### `localize-external-resources.cjs`
外部资源本地化
- 用法：`npm run intranet:localize`

### `verify-intranet-deployment.cjs`
内网部署验证
- 用法：`npm run verify:intranet`

### `restore-original-files.cjs`
恢复原始文件
- 用法：`npm run intranet:restore`

### `restore-localized-resources.cjs`
恢复本地化资源
- 用法：`npm run intranet:restore-localized`

## 🔧 配置和检查脚本

### `check-external-dependencies.cjs`
外部依赖检查
- 用法：`npm run intranet:check`

### `check-hardcoded-config.cjs`
硬编码配置检查
- 用法：`npm run config:check`

### `validate-env-config.cjs`
环境配置验证
- 用法：`npm run config:validate`

## ⚡ 优化脚本

### `run-all-optimizations.js`
运行所有优化
- 用法：`npm run optimize`

### `optimize-images.cjs`
图片优化
- 用法：`npm run optimize:images`

## 🗂️ 已清理的脚本

以下脚本已被移除（2025-09-05）：
- ~~`package-for-deployment.sh`~~ - 被 `simple-package.sh` 替代
- ~~`start-ldap-server.sh`~~ - 功能与 `start-ldap.js` 重复
- ~~`start-ldap-with-monitor.sh`~~ - PM2 监控版本，项目主要使用 Docker

## 📋 使用建议

### 日常开发
```bash
npm run dev:full          # 启动完整开发环境（前端 + LDAP）
npm run ldap               # 仅启动 LDAP 服务
```

### 构建部署
```bash
npm run build              # 标准构建
./scripts/simple-package.sh  # 创建部署包
```

### 内网部署
```bash
npm run intranet:prepare   # 准备内网部署
npm run build:intranet     # 内网构建
npm run verify:intranet    # 验证部署
```

### 配置管理
```bash
npm run config:setup      # 设置配置文件
npm run config:check      # 检查硬编码配置
npm run config:validate   # 验证环境配置
```