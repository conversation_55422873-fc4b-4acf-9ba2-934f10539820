#!/usr/bin/env node

import { execSync, spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const projectRoot = path.join(__dirname, '..');

// 配置选项
const config = {
  // 测试套件配置
  testSuites: [
    'compatibility.spec.js',
    'browser-feature-compatibility.spec.js', 
    'responsive-compatibility.spec.js',
    'cross-browser-interaction.spec.js'
  ],
  
  // 浏览器项目配置（从 playwright.config.js 同步）
  projects: [
    'Chrome 55', 'Chrome 70', 'Chrome 80', 'Chrome 90', 'Chrome 100', 'Chrome 110', 'Chrome Latest',
    'Firefox 52', 'Firefox 60 ESR', 'Firefox 78 ESR', 'Firefox 100', 'Firefox Latest',
    'Safari 10', 'Safari 14', 'Safari Latest',
    'Edge 15', 'Edge 79 (Chromium)', 'Edge Latest',
    'Mobile Chrome', 'Mobile Safari'
  ],
  
  // 报告配置
  reports: {
    outputDir: path.join(projectRoot, 'test-results'),
    htmlReport: 'compatibility-report.html',
    jsonReport: 'compatibility-results.json',
    summaryReport: 'compatibility-summary.txt'
  }
};

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function colorLog(message, color = 'white') {
  console.log(colors[color] + message + colors.reset);
}

// 确保输出目录存在
function ensureOutputDirectory() {
  if (!fs.existsSync(config.reports.outputDir)) {
    fs.mkdirSync(config.reports.outputDir, { recursive: true });
  }
}

// 检查依赖项
function checkDependencies() {
  colorLog('🔍 检查依赖项...', 'blue');
  
  try {
    // 检查 Playwright 是否安装
    execSync('npx playwright --version', { stdio: 'pipe' });
    colorLog('✅ Playwright 已安装', 'green');
  } catch (error) {
    colorLog('❌ Playwright 未安装，请运行: npm install @playwright/test', 'red');
    process.exit(1);
  }
  
  try {
    // 检查浏览器是否安装
    execSync('npx playwright install --dry-run', { stdio: 'pipe' });
    colorLog('✅ 浏览器已安装', 'green');
  } catch (error) {
    colorLog('⚠️ 某些浏览器可能未安装，将尝试安装...', 'yellow');
    try {
      execSync('npx playwright install', { stdio: 'inherit' });
      colorLog('✅ 浏览器安装完成', 'green');
    } catch (installError) {
      colorLog('❌ 浏览器安装失败', 'red');
      process.exit(1);
    }
  }
}

// 启动开发服务器
function startDevServer() {
  colorLog('🚀 启动开发服务器...', 'blue');
  
  return new Promise((resolve, reject) => {
    const server = spawn('npm', ['run', 'dev'], {
      cwd: projectRoot,
      stdio: 'pipe'
    });
    
    let serverReady = false;
    
    server.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('localhost:5173') || output.includes('Local:')) {
        if (!serverReady) {
          serverReady = true;
          colorLog('✅ 开发服务器已启动', 'green');
          resolve(server);
        }
      }
    });
    
    server.stderr.on('data', (data) => {
      const output = data.toString();
      if (output.includes('EADDRINUSE')) {
        if (!serverReady) {
          serverReady = true;
          colorLog('✅ 开发服务器已在运行', 'green');
          resolve(null); // 服务器已经在运行
        }
      }
    });
    
    // 超时处理
    setTimeout(() => {
      if (!serverReady) {
        reject(new Error('服务器启动超时'));
      }
    }, 30000);
    
    server.on('error', (error) => {
      if (!serverReady) {
        reject(error);
      }
    });
  });
}

// 运行兼容性测试
async function runCompatibilityTests(options = {}) {
  colorLog('🧪 开始运行兼容性测试...', 'blue');
  
  const {
    projects = config.projects,
    tests = config.testSuites,
    parallel = false,
    verbose = false
  } = options;
  
  const results = {
    startTime: new Date().toISOString(),
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    skippedTests: 0,
    projectResults: {}
  };
  
  for (const project of projects) {
    colorLog(`\n📊 测试项目: ${project}`, 'cyan');
    
    try {
      const projectArgs = [
        'playwright', 'test',
        '--project', `"${project}"`,
        '--reporter=json',
        ...tests.map(test => `tests/${test}`)
      ];
      
      if (verbose) {
        projectArgs.push('--verbose');
      }
      
      const output = execSync(`npx ${projectArgs.join(' ')}`, {
        cwd: projectRoot,
        stdio: 'pipe',
        encoding: 'utf8'
      });
      
      // 解析 JSON 输出
      try {
        const jsonOutput = JSON.parse(output);
        results.projectResults[project] = {
          status: 'passed',
          tests: jsonOutput.suites || [],
          stats: jsonOutput.stats || {}
        };
        
        results.totalTests += jsonOutput.stats?.expected || 0;
        results.passedTests += jsonOutput.stats?.passed || 0;
        results.failedTests += jsonOutput.stats?.failed || 0;
        results.skippedTests += jsonOutput.stats?.skipped || 0;
        
        colorLog(`✅ ${project} 测试完成`, 'green');
      } catch (parseError) {
        colorLog(`⚠️ ${project} 测试输出解析失败，但测试可能已完成`, 'yellow');
        results.projectResults[project] = {
          status: 'completed_with_parse_error',
          error: parseError.message
        };
      }
      
    } catch (error) {
      colorLog(`❌ ${project} 测试失败: ${error.message}`, 'red');
      results.projectResults[project] = {
        status: 'failed',
        error: error.message
      };
    }
  }
  
  results.endTime = new Date().toISOString();
  results.duration = new Date(results.endTime) - new Date(results.startTime);
  
  return results;
}

// 生成兼容性报告
function generateCompatibilityReport(results) {
  colorLog('📊 生成兼容性报告...', 'blue');
  
  // 生成 JSON 报告
  const jsonReportPath = path.join(config.reports.outputDir, config.reports.jsonReport);
  fs.writeFileSync(jsonReportPath, JSON.stringify(results, null, 2));
  
  // 生成文本摘要报告
  const summaryReport = generateTextSummary(results);
  const summaryReportPath = path.join(config.reports.outputDir, config.reports.summaryReport);
  fs.writeFileSync(summaryReportPath, summaryReport);
  
  // 生成 HTML 报告
  const htmlReport = generateHTMLReport(results);
  const htmlReportPath = path.join(config.reports.outputDir, config.reports.htmlReport);
  fs.writeFileSync(htmlReportPath, htmlReport);
  
  colorLog('✅ 兼容性报告生成完成', 'green');
  
  return {
    jsonReport: jsonReportPath,
    summaryReport: summaryReportPath,
    htmlReport: htmlReportPath
  };
}

// 生成文本摘要
function generateTextSummary(results) {
  const { projectResults, totalTests, passedTests, failedTests, skippedTests } = results;
  
  let summary = `
兼容性测试报告摘要
==================

测试时间: ${results.startTime} - ${results.endTime}
测试耗时: ${Math.round(results.duration / 1000)}秒
总计测试: ${totalTests}
通过测试: ${passedTests}
失败测试: ${failedTests}
跳过测试: ${skippedTests}
成功率: ${totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%

浏览器兼容性结果:
================

`;
  
  Object.entries(projectResults).forEach(([project, result]) => {
    summary += `${project}: ${result.status === 'passed' ? '✅ 通过' : 
                              result.status === 'failed' ? '❌ 失败' : 
                              result.status === 'completed_with_parse_error' ? '⚠️ 完成(解析错误)' : '❓ 未知'}\n`;
  });
  
  summary += `\n详细信息请查看 HTML 报告: ${config.reports.htmlReport}\n`;
  
  return summary;
}

// 生成 HTML 报告
function generateHTMLReport(results) {
  const { projectResults, totalTests, passedTests, failedTests } = results;
  
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器兼容性测试报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6; 
            color: #333; 
            background: #f5f5f5;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 40px 20px; 
            text-align: center; 
            margin: -20px -20px 30px -20px;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.1em; opacity: 0.9; }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px; 
        }
        .stat-card { 
            background: white; 
            padding: 25px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number { 
            font-size: 2.5em; 
            font-weight: bold; 
            margin-bottom: 5px; 
        }
        .stat-label { 
            color: #666; 
            font-size: 0.9em; 
            text-transform: uppercase; 
            letter-spacing: 1px;
        }
        .passed { color: #4caf50; }
        .failed { color: #f44336; }
        .skipped { color: #ff9800; }
        .total { color: #2196f3; }
        
        .browser-results { 
            background: white; 
            border-radius: 10px; 
            overflow: hidden; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .browser-results h2 { 
            background: #2196f3; 
            color: white; 
            padding: 20px; 
            margin: 0; 
        }
        .browser-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 1px; 
            background: #e0e0e0; 
            padding: 1px; 
        }
        .browser-item { 
            background: white; 
            padding: 20px; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .browser-name { font-weight: 600; }
        .status-badge { 
            padding: 5px 12px; 
            border-radius: 20px; 
            font-size: 0.85em; 
            font-weight: 600; 
        }
        .status-passed { background: #e8f5e8; color: #2e7d32; }
        .status-failed { background: #ffeaea; color: #c62828; }
        .status-warning { background: #fff3e0; color: #f57c00; }
        
        .timestamp { 
            text-align: center; 
            color: #666; 
            font-size: 0.9em; 
            margin-top: 30px; 
            padding-top: 20px; 
            border-top: 1px solid #e0e0e0; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>浏览器兼容性测试报告</h1>
            <p>YNNX AI 开发平台 - 全面兼容性测试结果</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number total">${totalTests}</div>
                <div class="stat-label">总计测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number passed">${passedTests}</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number failed">${failedTests}</div>
                <div class="stat-label">失败测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>
        
        <div class="browser-results">
            <h2>浏览器兼容性结果</h2>
            <div class="browser-grid">
                ${Object.entries(projectResults).map(([project, result]) => `
                <div class="browser-item">
                    <span class="browser-name">${project}</span>
                    <span class="status-badge ${
                      result.status === 'passed' ? 'status-passed' :
                      result.status === 'failed' ? 'status-failed' : 'status-warning'
                    }">
                        ${result.status === 'passed' ? '✅ 通过' :
                          result.status === 'failed' ? '❌ 失败' : '⚠️ 警告'}
                    </span>
                </div>
                `).join('')}
            </div>
        </div>
        
        <div class="timestamp">
            报告生成时间: ${new Date().toLocaleString('zh-CN')}
        </div>
    </div>
</body>
</html>
`;
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  // 解析命令行参数
  const options = {
    projects: config.projects,
    tests: config.testSuites,
    parallel: args.includes('--parallel'),
    verbose: args.includes('--verbose'),
    skipServer: args.includes('--skip-server')
  };
  
  // 检查是否指定了特定项目
  const projectArg = args.find(arg => arg.startsWith('--project='));
  if (projectArg) {
    options.projects = [projectArg.split('=')[1]];
  }
  
  // 检查是否指定了特定测试
  const testArg = args.find(arg => arg.startsWith('--test='));
  if (testArg) {
    options.tests = [testArg.split('=')[1]];
  }
  
  try {
    colorLog('🚀 开始浏览器兼容性测试流程', 'cyan');
    
    // 1. 检查依赖
    checkDependencies();
    
    // 2. 确保输出目录存在
    ensureOutputDirectory();
    
    // 3. 启动开发服务器（如果需要）
    let server = null;
    if (!options.skipServer) {
      server = await startDevServer();
    }
    
    try {
      // 4. 运行兼容性测试
      const results = await runCompatibilityTests(options);
      
      // 5. 生成报告
      const reportPaths = generateCompatibilityReport(results);
      
      // 6. 显示结果摘要
      colorLog('\n📊 测试完成！结果摘要:', 'cyan');
      colorLog(`总计测试: ${results.totalTests}`, 'white');
      colorLog(`通过测试: ${results.passedTests}`, 'green');
      colorLog(`失败测试: ${results.failedTests}`, results.failedTests > 0 ? 'red' : 'white');
      colorLog(`成功率: ${results.totalTests > 0 ? Math.round((results.passedTests / results.totalTests) * 100) : 0}%`, 'cyan');
      
      colorLog('\n📄 报告文件:', 'blue');
      colorLog(`HTML 报告: ${reportPaths.htmlReport}`, 'white');
      colorLog(`JSON 报告: ${reportPaths.jsonReport}`, 'white');
      colorLog(`摘要报告: ${reportPaths.summaryReport}`, 'white');
      
    } finally {
      // 7. 清理服务器
      if (server) {
        colorLog('🛑 关闭开发服务器...', 'yellow');
        server.kill();
      }
    }
    
    colorLog('✅ 兼容性测试流程完成！', 'green');
    
  } catch (error) {
    colorLog(`❌ 测试过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 如果直接运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { main, runCompatibilityTests, generateCompatibilityReport };