#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const projectRoot = path.join(__dirname, '..');

// Chrome 版本兼容性分析器配置
const config = {
  chromeVersions: [
    {
      name: 'Chrome 50',
      version: '50.0.2661.102',
      releaseDate: '2016年4月',
      keyFeatures: ['Proxies', 'Spread operator', 'Rest parameters'],
      majorChanges: ['ES6 基础特性支持', 'CSS3 动画性能提升']
    },
    {
      name: 'Chrome 60',
      version: '60.0.3112.113', 
      releaseDate: '2017年7月',
      keyFeatures: ['ES2017 (async/await)', 'CSS Grid Layout', 'Payment Request API'],
      majorChanges: ['异步编程标准化', 'CSS 网格布局完整支持', '支付接口标准化']
    },
    {
      name: 'Chrome 70',
      version: '70.0.3538.110',
      releaseDate: '2018年10月', 
      keyFeatures: ['Web Authentication API', 'BigInt', 'CSS Logical Properties'],
      majorChanges: ['生物识别认证支持', '大数处理能力', '国际化布局支持']
    },
    {
      name: 'Chrome 80',
      version: '80.0.3987.149',
      releaseDate: '2020年2月',
      keyFeatures: ['Optional Chaining (?.)', 'Nullish Coalescing (??)', 'ES2020'],
      majorChanges: ['空值安全访问', '现代 JavaScript 语法', 'SameSite Cookie 默认值']
    },
    {
      name: 'Chrome 90', 
      version: '90.0.4430.212',
      releaseDate: '2021年4月',
      keyFeatures: ['CSS aspect-ratio', 'Logical Assignment', 'CSS :is() selector'],
      majorChanges: ['响应式设计增强', '逻辑赋值运算符', 'CSS 选择器优化']
    },
    {
      name: 'Chrome 100',
      version: '100.0.4896.127',
      releaseDate: '2022年3月',
      keyFeatures: ['Container Queries', 'ES2022 Class Fields', 'CSS @layer'],
      majorChanges: ['容器查询支持', '类字段语法', 'CSS 层叠管理']
    }
  ],
  outputDir: path.join(projectRoot, 'chrome-compatibility-reports'),
  reportTypes: ['summary', 'detailed', 'comparison', 'html']
};

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m', 
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(message, color = 'reset') {
  console.log(colors[color] + message + colors.reset);
}

// 运行单个 Chrome 版本测试
async function runChromeVersionTest(versionName) {
  colorLog(`🧪 测试 ${versionName}...`, 'blue');
  
  try {
    const output = execSync(
      `npx playwright test --project="${versionName}" --reporter=json tests/chrome-versions-compatibility.spec.js`,
      { 
        cwd: projectRoot,
        encoding: 'utf8',
        stdio: 'pipe'
      }
    );
    
    // 解析测试输出
    const lines = output.split('\n');
    const jsonLine = lines.find(line => line.trim().startsWith('{') && line.includes('suites'));
    
    if (jsonLine) {
      const testResult = JSON.parse(jsonLine);
      return {
        version: versionName,
        success: true,
        result: testResult
      };
    }
    
    return {
      version: versionName,
      success: true,
      result: { summary: 'Test completed but no JSON output found' }
    };
    
  } catch (error) {
    colorLog(`❌ ${versionName} 测试失败: ${error.message}`, 'red');
    return {
      version: versionName,
      success: false,
      error: error.message
    };
  }
}

// 运行所有 Chrome 版本测试
async function runAllChromeVersionTests() {
  colorLog('🚀 开始 Chrome 版本兼容性分析...', 'cyan');
  
  const results = {};
  
  for (const versionConfig of config.chromeVersions) {
    const result = await runChromeVersionTest(versionConfig.name);
    results[versionConfig.name] = {
      ...result,
      config: versionConfig
    };
    
    // 添加延迟避免资源冲突
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  return results;
}

// 生成版本对比分析
function generateVersionComparison(results) {
  const comparison = {
    timestamp: new Date().toISOString(),
    totalVersions: Object.keys(results).length,
    successfulTests: Object.values(results).filter(r => r.success).length,
    failedTests: Object.values(results).filter(r => !r.success).length,
    versionAnalysis: {},
    evolutionAnalysis: {
      featureProgression: {},
      performanceEvolution: {},
      compatibilityTrends: {}
    }
  };
  
  // 分析每个版本
  Object.entries(results).forEach(([versionName, versionData]) => {
    if (versionData.success && versionData.config) {
      const config = versionData.config;
      
      comparison.versionAnalysis[versionName] = {
        version: config.version,
        releaseDate: config.releaseDate,
        keyFeatures: config.keyFeatures,
        majorChanges: config.majorChanges,
        testStatus: 'passed',
        significance: calculateVersionSignificance(config)
      };
    } else {
      comparison.versionAnalysis[versionName] = {
        testStatus: 'failed',
        error: versionData.error || 'Unknown error'
      };
    }
  });
  
  // 特性演进分析
  comparison.evolutionAnalysis.featureProgression = {
    'ES6+ JavaScript': {
      'Chrome 50': ['Proxies', 'Spread operator'],
      'Chrome 60': ['async/await', 'Object.entries/values'],
      'Chrome 70': ['BigInt', 'Object rest/spread'],
      'Chrome 80': ['Optional Chaining', 'Nullish Coalescing'],
      'Chrome 90': ['Logical Assignment', 'String.replaceAll'],
      'Chrome 100': ['Class Fields', 'Private Fields']
    },
    'CSS Features': {
      'Chrome 50': ['Flexbox', 'CSS3 Transforms'],
      'Chrome 60': ['CSS Grid Layout'],
      'Chrome 70': ['CSS Variables', 'Logical Properties'],
      'Chrome 80': ['CSS Environment Variables'],
      'Chrome 90': ['aspect-ratio', ':is() selector'],
      'Chrome 100': ['Container Queries', '@layer']
    },
    'Web APIs': {
      'Chrome 50': ['Fetch API', 'Service Worker'],
      'Chrome 60': ['Payment Request API', 'Intersection Observer'],
      'Chrome 70': ['Web Authentication API'],
      'Chrome 80': ['Web Locks API'],
      'Chrome 90': ['Web Codecs API'],
      'Chrome 100': ['File System Access API']
    }
  };
  
  return comparison;
}

// 计算版本重要性
function calculateVersionSignificance(config) {
  const significanceFactors = {
    majorESVersion: config.keyFeatures.some(f => f.includes('ES20')) ? 3 : 0,
    cssGridSupport: config.keyFeatures.some(f => f.includes('Grid')) ? 2 : 0,
    asyncAwaitSupport: config.keyFeatures.some(f => f.includes('async')) ? 3 : 0,
    modernSyntax: config.keyFeatures.some(f => f.includes('Optional') || f.includes('Nullish')) ? 2 : 0,
    apiCount: config.keyFeatures.length
  };
  
  const totalScore = Object.values(significanceFactors).reduce((sum, score) => sum + score, 0);
  
  if (totalScore >= 8) return 'Critical';
  if (totalScore >= 5) return 'Important';
  if (totalScore >= 3) return 'Moderate';
  return 'Minor';
}

// 生成详细 HTML 报告
function generateHTMLReport(comparison) {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome 版本兼容性分析报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6; 
            color: #333; 
            background: #f8f9fa;
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { 
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white; 
            padding: 40px 20px; 
            text-align: center; 
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 15px 15px;
        }
        .header h1 { font-size: 2.8em; margin-bottom: 15px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        
        .summary-cards { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 25px; 
            margin-bottom: 40px; 
        }
        .summary-card { 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #4285f4;
        }
        .summary-number { 
            font-size: 3em; 
            font-weight: bold; 
            color: #4285f4;
            margin-bottom: 10px; 
        }
        .summary-label { 
            color: #666; 
            font-size: 1.1em; 
            text-transform: uppercase; 
            letter-spacing: 1px;
        }
        
        .version-timeline { 
            background: white; 
            border-radius: 12px; 
            overflow: hidden; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }
        .timeline-header { 
            background: #4285f4; 
            color: white; 
            padding: 25px; 
            font-size: 1.4em;
            font-weight: 600;
        }
        .timeline-content { padding: 30px; }
        .version-item { 
            display: flex; 
            align-items: flex-start;
            padding: 25px 0; 
            border-bottom: 1px solid #eee;
            position: relative;
        }
        .version-item:last-child { border-bottom: none; }
        .version-date { 
            min-width: 120px; 
            font-weight: 600; 
            color: #4285f4;
            font-size: 1.1em;
        }
        .version-details { flex: 1; margin-left: 30px; }
        .version-name { 
            font-size: 1.3em; 
            font-weight: 700; 
            margin-bottom: 8px; 
            color: #333;
        }
        .version-features { 
            margin-bottom: 12px; 
        }
        .feature-tag { 
            display: inline-block; 
            background: #e8f0fe; 
            color: #1967d2;
            padding: 4px 12px; 
            border-radius: 20px; 
            font-size: 0.85em; 
            margin: 2px 4px 2px 0;
            font-weight: 500;
        }
        .significance-badge {
            position: absolute;
            top: 25px;
            right: 0;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .critical { background: #fce8e6; color: #d93025; }
        .important { background: #fef7e0; color: #f9ab00; }
        .moderate { background: #e6f4ea; color: #137333; }
        .minor { background: #f1f3f4; color: #5f6368; }
        
        .evolution-section { 
            background: white; 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }
        .evolution-header { 
            background: #34a853; 
            color: white; 
            padding: 25px; 
            font-size: 1.4em;
            font-weight: 600;
        }
        .evolution-content { padding: 30px; }
        .feature-category { margin-bottom: 35px; }
        .feature-category h3 { 
            color: #34a853; 
            margin-bottom: 20px; 
            font-size: 1.3em;
            border-bottom: 2px solid #34a853;
            padding-bottom: 8px;
        }
        .evolution-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
        }
        .evolution-item { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px;
            border-left: 4px solid #34a853;
        }
        .evolution-version { 
            font-weight: 700; 
            color: #34a853;
            margin-bottom: 10px; 
        }
        .evolution-features { 
            list-style: none; 
        }
        .evolution-features li { 
            padding: 3px 0; 
            position: relative;
            padding-left: 20px;
        }
        .evolution-features li:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #34a853;
            font-size: 0.8em;
        }
        
        .footer { 
            text-align: center; 
            color: #666; 
            font-size: 0.9em; 
            margin-top: 50px; 
            padding-top: 30px; 
            border-top: 1px solid #eee; 
        }
        
        @media (max-width: 768px) {
            .container { padding: 15px; }
            .header h1 { font-size: 2.2em; }
            .summary-cards { grid-template-columns: 1fr; }
            .version-item { flex-direction: column; }
            .version-details { margin-left: 0; margin-top: 15px; }
            .significance-badge { position: relative; margin-top: 15px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Chrome 版本兼容性分析报告</h1>
            <p>YNNX AI 开发平台 - Chrome 50+ 到 100+ 版本演进分析</p>
        </div>
        
        <div class="summary-cards">
            <div class="summary-card">
                <div class="summary-number">${comparison.totalVersions}</div>
                <div class="summary-label">测试版本数</div>
            </div>
            <div class="summary-card">
                <div class="summary-number">${comparison.successfulTests}</div>
                <div class="summary-label">成功测试</div>
            </div>
            <div class="summary-card">
                <div class="summary-number">${comparison.failedTests}</div>
                <div class="summary-label">失败测试</div>
            </div>
            <div class="summary-card">
                <div class="summary-number">${Math.round((comparison.successfulTests / comparison.totalVersions) * 100)}%</div>
                <div class="summary-label">成功率</div>
            </div>
        </div>
        
        <div class="version-timeline">
            <div class="timeline-header">Chrome 版本演进时间线</div>
            <div class="timeline-content">
                ${Object.entries(comparison.versionAnalysis)
                  .filter(([_, data]) => data.testStatus === 'passed')
                  .map(([versionName, data]) => `
                <div class="version-item">
                    <div class="version-date">${data.releaseDate}</div>
                    <div class="version-details">
                        <div class="version-name">${versionName}</div>
                        <div class="version-features">
                            ${data.keyFeatures.map(feature => 
                              `<span class="feature-tag">${feature}</span>`
                            ).join('')}
                        </div>
                        <div class="major-changes">
                            ${data.majorChanges ? data.majorChanges.join(' • ') : ''}
                        </div>
                    </div>
                    <div class="significance-badge ${data.significance.toLowerCase()}">
                        ${data.significance}
                    </div>
                </div>
                `).join('')}
            </div>
        </div>
        
        <div class="evolution-section">
            <div class="evolution-header">特性演进分析</div>
            <div class="evolution-content">
                ${Object.entries(comparison.evolutionAnalysis.featureProgression).map(([category, versions]) => `
                <div class="feature-category">
                    <h3>${category}</h3>
                    <div class="evolution-grid">
                        ${Object.entries(versions).map(([version, features]) => `
                        <div class="evolution-item">
                            <div class="evolution-version">${version}</div>
                            <ul class="evolution-features">
                                ${features.map(feature => `<li>${feature}</li>`).join('')}
                            </ul>
                        </div>
                        `).join('')}
                    </div>
                </div>
                `).join('')}
            </div>
        </div>
        
        <div class="footer">
            <p>报告生成时间: ${new Date().toLocaleString('zh-CN')}</p>
            <p>基于 Playwright 测试框架 | YNNX AI 开发平台兼容性测试</p>
        </div>
    </div>
</body>
</html>
`;
}

// 保存报告文件
function saveReports(comparison) {
  // 确保输出目录存在
  if (!fs.existsSync(config.outputDir)) {
    fs.mkdirSync(config.outputDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reports = {};
  
  // JSON 详细报告
  const jsonPath = path.join(config.outputDir, `chrome-compatibility-${timestamp}.json`);
  fs.writeFileSync(jsonPath, JSON.stringify(comparison, null, 2));
  reports.json = jsonPath;
  
  // HTML 报告
  const htmlContent = generateHTMLReport(comparison);
  const htmlPath = path.join(config.outputDir, `chrome-compatibility-${timestamp}.html`);
  fs.writeFileSync(htmlPath, htmlContent);
  reports.html = htmlPath;
  
  // 文本摘要
  const summary = generateTextSummary(comparison);
  const summaryPath = path.join(config.outputDir, `chrome-compatibility-summary-${timestamp}.txt`);
  fs.writeFileSync(summaryPath, summary);
  reports.summary = summaryPath;
  
  return reports;
}

// 生成文本摘要
function generateTextSummary(comparison) {
  let summary = `
Chrome 版本兼容性分析摘要
=======================

分析时间: ${new Date(comparison.timestamp).toLocaleString('zh-CN')}
测试版本: ${comparison.totalVersions} 个
成功测试: ${comparison.successfulTests} 个
失败测试: ${comparison.failedTests} 个
成功率: ${Math.round((comparison.successfulTests / comparison.totalVersions) * 100)}%

版本演进关键节点:
================

`;

  Object.entries(comparison.versionAnalysis)
    .filter(([_, data]) => data.testStatus === 'passed')
    .forEach(([versionName, data]) => {
      summary += `${versionName} (${data.releaseDate}) - 重要性: ${data.significance}\n`;
      summary += `  关键特性: ${data.keyFeatures.join(', ')}\n`;
      if (data.majorChanges) {
        summary += `  主要变化: ${data.majorChanges.join(', ')}\n`;
      }
      summary += '\n';
    });

  summary += `
特性演进趋势:
============

JavaScript (ES6+):
- Chrome 50: 基础 ES6 特性 (Proxies, Spread)
- Chrome 60: ES2017 异步编程 (async/await)
- Chrome 70: 大数支持 (BigInt)
- Chrome 80: 空值安全访问 (Optional Chaining, Nullish Coalescing)
- Chrome 90: 现代语法糖 (Logical Assignment)
- Chrome 100: 类字段语法 (Class Fields, Private Fields)

CSS 布局:
- Chrome 50: Flexbox 成熟
- Chrome 60: CSS Grid 完整支持
- Chrome 70: CSS 变量和逻辑属性
- Chrome 90: aspect-ratio 和 :is() 选择器
- Chrome 100: Container Queries 容器查询

Web APIs:
- Chrome 50: Fetch API, Service Worker 基础
- Chrome 60: Payment Request API 支付标准化
- Chrome 70: Web Authentication API 生物识别
- Chrome 80: Web Locks API 资源锁定
- Chrome 90: Web Codecs API 媒体处理
- Chrome 100: File System Access API 文件系统

建议:
====
1. 最低支持版本建议: Chrome 60+ (支持 async/await 和 CSS Grid)
2. 推荐目标版本: Chrome 80+ (现代 JavaScript 语法完整支持)
3. 前沿特性使用: Chrome 90+ (最新布局和 API 特性)
4. 渐进增强策略: 针对老版本提供降级方案

详细报告请查看对应的 HTML 和 JSON 文件。
`;

  return summary;
}

// 主函数
async function main() {
  try {
    colorLog('🌟 Chrome 版本兼容性分析器', 'cyan');
    colorLog('==========================', 'cyan');
    
    // 确保输出目录存在
    if (!fs.existsSync(config.outputDir)) {
      fs.mkdirSync(config.outputDir, { recursive: true });
      colorLog(`📁 创建输出目录: ${config.outputDir}`, 'blue');
    }
    
    // 运行所有版本测试
    const results = await runAllChromeVersionTests();
    
    // 生成对比分析
    colorLog('📊 生成对比分析...', 'blue');
    const comparison = generateVersionComparison(results);
    
    // 保存报告
    colorLog('💾 保存分析报告...', 'blue');
    const reportPaths = saveReports(comparison);
    
    // 输出结果
    colorLog('\n✅ Chrome 版本兼容性分析完成！', 'green');
    colorLog(`📈 分析了 ${comparison.totalVersions} 个版本`, 'green');
    colorLog(`✅ 成功测试 ${comparison.successfulTests} 个版本`, 'green');
    if (comparison.failedTests > 0) {
      colorLog(`❌ 失败测试 ${comparison.failedTests} 个版本`, 'red');
    }
    
    colorLog('\n📄 生成的报告文件:', 'blue');
    colorLog(`HTML 报告: ${reportPaths.html}`, 'cyan');
    colorLog(`JSON 报告: ${reportPaths.json}`, 'cyan'); 
    colorLog(`文本摘要: ${reportPaths.summary}`, 'cyan');
    
    colorLog('\n🔗 快速查看报告:', 'blue');
    colorLog(`open ${reportPaths.html}`, 'yellow');
    
  } catch (error) {
    colorLog(`❌ 分析过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 如果直接运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { main, runAllChromeVersionTests, generateVersionComparison };