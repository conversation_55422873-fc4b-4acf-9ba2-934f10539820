# Web IDE 使用说明功能

## 功能概述
在Web IDE标题下方、IDE容器上方添加了详细的使用说明面板，帮助用户快速了解如何使用Web IDE中的各种功能，包括Git代码管理、AI插件配置、快捷键操作等。

## 功能特性

### 1. 智能显示控制
- **首次访问**: 默认显示使用说明
- **用户偏好**: 记住用户的隐藏偏好，下次访问不重复显示
- **手动控制**: 用户可以随时显示/隐藏说明面板
- **响应式设计**: 仅在非全屏模式下显示

### 2. 内容模块化
使用说明分为四个主要模块：

#### 📝 代码管理
- Git仓库克隆指导
- 内部仓库使用说明
- 代码管理最佳实践

#### 🔑 AI插件配置  
- API密钥获取指导
- Cline和RooCode插件配置
- API地址配置说明

#### ⌨️ 快捷键操作
- F11全屏切换
- F5/Ctrl+R刷新IDE
- 标准VSCode快捷键支持

#### 📦 插件安装
- RooCode插件下载安装
- Cline插件安装指导
- AI辅助编程配置

### 3. 快速导航
- **快速链接**: 直接跳转到API密钥管理、工具下载、文档中心
- **平滑滚动**: 使用smooth scroll提供良好的用户体验
- **页面内导航**: 无需离开当前页面即可访问相关功能

## 技术实现

### 状态管理
```javascript
const [showGuide, setShowGuide] = useState(() => {
  // 从localStorage读取用户偏好，默认显示说明
  try {
    const saved = localStorage.getItem('webide-guide-hidden');
    return saved !== 'true';
  } catch {
    return true;
  }
});
```

### 本地存储集成
```javascript
// 隐藏使用说明并保存偏好
const hideGuide = () => {
  setShowGuide(false);
  try {
    localStorage.setItem('webide-guide-hidden', 'true');
  } catch (error) {
    console.warn('无法保存用户偏好到localStorage:', error);
  }
};

// 显示使用说明
const showGuidePanel = () => {
  setShowGuide(true);
  try {
    localStorage.removeItem('webide-guide-hidden');
  } catch (error) {
    console.warn('无法更新localStorage:', error);
  }
};
```

### 响应式布局
```javascript
{user && showGuide && !isFullscreen && (
  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-4 relative">
    {/* 使用说明内容 */}
  </div>
)}
```

### 网格布局系统
```javascript
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
  {/* 四个功能模块 */}
</div>
```

## 用户界面设计

### 视觉层次
1. **主标题**: "Web IDE 使用指南" - 清晰的功能标识
2. **模块卡片**: 每个功能模块独立的卡片设计
3. **图标系统**: 每个模块配有相应的图标，提高识别度
4. **颜色编码**: 不同模块使用不同颜色的图标区分

### 交互元素
1. **关闭按钮**: 右上角X按钮，支持快速隐藏
2. **显示按钮**: header中的问号图标，支持重新显示
3. **快速链接**: 底部的导航链接，支持页面内跳转
4. **确认按钮**: "我知道了"按钮，友好的确认方式

### 响应式适配
- **移动端**: 单列布局，内容垂直排列
- **平板端**: 双列布局，平衡内容密度
- **桌面端**: 四列布局，充分利用屏幕空间

## 内容指导

### 代码管理模块
```
使用Git从内部仓库克隆代码：
git clone <内部仓库地址>
```

### API配置模块
```
配置Cline和RooCode插件：
• 在"API密钥管理"页面获取个人密钥
• API地址：http://132.147.223.249:4000
• 在插件设置中配置上述信息
```

### 快捷键模块
```
• F11 全屏切换
• F5 / Ctrl+R 刷新IDE
• 标准VSCode快捷键支持
```

### 插件安装模块
```
安装AI助手插件：
• 在"工具下载"页面获取RooCode插件
• 扩展面板中安装Cline插件
• 配置完成后即可使用AI辅助编程
```

## 用户体验优化

### 1. 渐进式引导
- 首次访问显示完整说明
- 用户熟悉后可以隐藏
- 需要时可以随时重新显示

### 2. 上下文相关
- 只在登录用户可见
- 只在非全屏模式显示
- 与当前功能状态相关

### 3. 无障碍设计
- 清晰的视觉层次
- 良好的颜色对比度
- 键盘导航支持
- 屏幕阅读器友好

### 4. 性能考虑
- 轻量级实现，不影响IDE加载
- 本地存储优化，减少重复渲染
- 条件渲染，按需显示内容

## 维护和扩展

### 内容更新
- 模块化设计便于内容更新
- 新功能可以轻松添加新的指导模块
- 支持多语言扩展

### 样式定制
- 使用Tailwind CSS，易于样式调整
- 支持深色模式
- 响应式设计自动适配

### 功能扩展
- 可以添加视频教程链接
- 支持交互式引导
- 可以集成用户反馈功能

## 总结

Web IDE使用说明功能显著提升了用户的上手体验：

### ✅ 用户价值
- **降低学习成本**: 清晰的功能指导
- **提高效率**: 快速找到所需功能
- **减少困惑**: 详细的配置说明
- **增强信心**: 完整的使用流程

### ✅ 技术特点
- **智能显示**: 基于用户偏好的显示控制
- **响应式设计**: 适配各种屏幕尺寸
- **模块化架构**: 易于维护和扩展
- **性能优化**: 轻量级实现

### ✅ 业务价值
- **提升用户体验**: 降低使用门槛
- **减少支持成本**: 自助式问题解决
- **增加用户粘性**: 更好的首次体验
- **促进功能使用**: 引导用户发现更多功能

用户现在可以通过直观的使用说明快速掌握Web IDE的各项功能，从Git代码管理到AI插件配置，一目了然！
