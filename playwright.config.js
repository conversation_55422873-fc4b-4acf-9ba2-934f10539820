import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  fullyParallel: false, // 避免并发冲突
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 2 : 1, // CI环境增加并发度
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/compatibility-report.json' }],
    ['junit', { outputFile: 'test-results/compatibility-junit.xml' }],
    ['list'],
    ['github'] // GitHub Actions 集成
  ],
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 10000,
    navigationTimeout: 30000
  },
  projects: [
    // === 重点关注的 Chrome 版本系列 ===
    
    // Chrome 50 (2016年4月) - 基准老版本, Proxies, Spread operator
    {
      name: 'Chrome 50',
      use: {
        ...devices['Desktop Chrome'],
        channel: 'chrome',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36',
        viewport: { width: 1280, height: 720 },
        extraHTTPHeaders: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36'
        }
      }
    },
    
    // Chrome 60 (2017年7月) - ES2017 (async/await), CSS Grid Layout
    {
      name: 'Chrome 60',
      use: {
        ...devices['Desktop Chrome'],
        channel: 'chrome',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36',
        viewport: { width: 1280, height: 720 },
        extraHTTPHeaders: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36'
        }
      }
    },
    
    // Chrome 70 (2018年10月) - CSS Grid完全支持, Web Authentication API
    {
      name: 'Chrome 70',
      use: {
        ...devices['Desktop Chrome'],
        channel: 'chrome',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36',
        viewport: { width: 1280, height: 720 },
        extraHTTPHeaders: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36'
        }
      }
    },
    
    // Chrome 80 (2020年2月) - ES2020, Optional Chaining, Nullish Coalescing
    {
      name: 'Chrome 80',
      use: {
        ...devices['Desktop Chrome'],
        channel: 'chrome',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36',
        viewport: { width: 1280, height: 720 },
        extraHTTPHeaders: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36'
        }
      }
    },
    
    // Chrome 90 (2021年4月) - ES2021, Logical Assignment, CSS aspect-ratio
    {
      name: 'Chrome 90',
      use: {
        ...devices['Desktop Chrome'],
        channel: 'chrome',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
        viewport: { width: 1280, height: 720 },
        extraHTTPHeaders: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36'
        }
      }
    },
    
    // Chrome 100 (2022年3月) - ES2022, Container Queries (experimental)
    {
      name: 'Chrome 100',
      use: {
        ...devices['Desktop Chrome'],
        channel: 'chrome',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
        viewport: { width: 1280, height: 720 },
        extraHTTPHeaders: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36'
        }
      }
    },
    
    // Chrome Latest - 最新版本对比测试
    {
      name: 'Chrome Latest',
      use: {
        ...devices['Desktop Chrome'],
        channel: 'chrome'
      }
    },

    // === Firefox 系列 ===
    // Firefox 52 (2017年3月) - 最低支持版本
    {
      name: 'Firefox 52',
      use: {
        ...devices['Desktop Firefox'],
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:52.0) Gecko/20100101 Firefox/52.0',
        viewport: { width: 1280, height: 720 }
      }
    },
    // Firefox 60 ESR (2018年5月) - 长期支持版本
    {
      name: 'Firefox 60 ESR',
      use: {
        ...devices['Desktop Firefox'],
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:60.0) Gecko/20100101 Firefox/60.0',
        viewport: { width: 1280, height: 720 }
      }
    },
    // Firefox 78 ESR (2020年7月) - 企业版本
    {
      name: 'Firefox 78 ESR',
      use: {
        ...devices['Desktop Firefox'],
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:78.0) Gecko/20100101 Firefox/78.0',
        viewport: { width: 1280, height: 720 }
      }
    },
    // Firefox 100 (2022年5月)
    {
      name: 'Firefox 100',
      use: {
        ...devices['Desktop Firefox'],
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:100.0) Gecko/20100101 Firefox/100.0',
        viewport: { width: 1280, height: 720 }
      }
    },
    // Firefox Latest
    {
      name: 'Firefox Latest',
      use: {
        ...devices['Desktop Firefox']
      }
    },

    // === Safari 系列 ===
    // Safari 10 (2016年9月) - 最低支持版本
    {
      name: 'Safari 10',
      use: {
        ...devices['Desktop Safari'],
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/603.3.8 (KHTML, like Gecko) Version/10.1.2 Safari/603.3.8',
        viewport: { width: 1280, height: 720 }
      }
    },
    // Safari 14 (2020年9月) - 现代特性支持
    {
      name: 'Safari 14',
      use: {
        ...devices['Desktop Safari'],
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15',
        viewport: { width: 1280, height: 720 }
      }
    },
    // Safari Latest
    {
      name: 'Safari Latest',
      use: {
        ...devices['Desktop Safari']
      }
    },

    // === Edge 系列 ===
    // Edge 15 (2017年4月) - 最低支持版本
    {
      name: 'Edge 15',
      use: {
        ...devices['Desktop Edge'],
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Safari/537.36 Edge/15.15063',
        viewport: { width: 1280, height: 720 }
      }
    },
    // Edge 79 (2020年1月) - Chromium 版本
    {
      name: 'Edge 79 (Chromium)',
      use: {
        ...devices['Desktop Edge'],
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.74 Safari/537.36 Edg/79.0.309.43',
        viewport: { width: 1280, height: 720 }
      }
    },
    // Edge Latest
    {
      name: 'Edge Latest',
      use: {
        ...devices['Desktop Edge']
      }
    },

    // === 移动设备测试 ===
    {
      name: 'Mobile Chrome',
      use: {
        ...devices['Pixel 5']
      }
    },
    {
      name: 'Mobile Safari',
      use: {
        ...devices['iPhone 12']
      }
    },

    // === 不同分辨率测试 ===
    {
      name: '1366x768 (Common Laptop)',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1366, height: 768 }
      }
    },
    {
      name: '1920x1080 (Full HD)',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 }
      }
    },
    {
      name: '2560x1440 (2K)',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 2560, height: 1440 }
      }
    }
  ],
  webServer: {
    command: 'npm run dev',
    port: 5173,
    reuseExistingServer: !process.env.CI,
    timeout: 60000
  }
});