import { chromium } from 'playwright';

async function testYNNXLoader() {
  console.log('🚀 启动 YNNX 加载器测试...');
  
  const browser = await chromium.launch({
    headless: true,
    args: ['--ignore-certificate-errors', '--ignore-ssl-errors', '--ignore-certificate-errors-spki-list']
  });
  
  const context = await browser.newContext({
    ignoreHTTPSErrors: true
  });
  
  const page = await context.newPage();
  
  try {
    console.log('📡 导航到本地服务器...');
    await page.goto('https://127.0.0.1', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log('🔍 等待加载器出现...');
    await page.waitForSelector('div[class*="fixed inset-0 z-50"]', { timeout: 10000 });
    
    console.log('🎯 查找 YNNX 像素网格...');
    const pixelGrid = await page.locator('div[style*="grid-template"]').first();
    
    if (await pixelGrid.isVisible()) {
      console.log('✅ 像素网格已找到');
      
      // 计算网格尺寸
      const gridStyle = await pixelGrid.getAttribute('style');
      const columnsMatch = gridStyle.match(/repeat\((\d+), 1fr\)/);
      const columns = columnsMatch ? parseInt(columnsMatch[1]) : 0;
      
      console.log(`📐 网格列数: ${columns}`);
      
      if (columns >= 30) {
        console.log('✅ YNNX 字母网格完整！(30列)');
      } else if (columns >= 15) {
        console.log('⚠️  使用简化网格 (15列)');
      } else if (columns >= 9) {
        console.log('⚠️  使用最小网格 (9列)');
      } else {
        console.log('❌ 网格太小，可能无法显示完整 YNNX');
      }
      
      // 检查像素元素
      const pixels = await page.locator('div[class*="w-"][class*="h-"][class*="rounded-sm"]').count();
      console.log(`🔢 像素数量: ${pixels}`);
      
      // 检查是否有彩色像素（激活状态）
      const activePixels = await page.locator('div[class*="bg-cyan"], div[class*="bg-gradient"], div[class*="bg-blue"], div[class*="bg-purple"]').count();
      console.log(`🌈 激活像素数量: ${activePixels}`);
      
      if (activePixels > 0) {
        console.log('✅ YNNX 字母正在显示！');
      } else {
        console.log('⏳ 等待字母动画...');
        await page.waitForTimeout(2000);
        const activePixelsRetry = await page.locator('div[class*="bg-cyan"], div[class*="bg-gradient"], div[class*="bg-blue"], div[class*="bg-purple"]').count();
        console.log(`🌈 重试后激活像素数量: ${activePixelsRetry}`);
      }
      
    } else {
      console.log('❌ 未找到像素网格');
    }
    
    // 检查加载文本
    const loadingText = await page.locator('text=YNNX AI 智能平台正在启动').first();
    if (await loadingText.isVisible()) {
      console.log('✅ 找到加载文本："YNNX AI 智能平台正在启动"');
    }
    
    // 等待加载完成
    console.log('⏳ 等待加载完成...');
    await page.waitForTimeout(5000);
    
    // 检查是否进入主页面
    const isLoaderGone = await page.locator('div[class*="fixed inset-0 z-50"]').count() === 0;
    if (isLoaderGone) {
      console.log('✅ 加载器已消失，进入主页面');
    } else {
      console.log('⏳ 加载器仍在显示');
    }
    
    console.log('🎉 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await browser.close();
  }
}

testYNNXLoader();