// Polyfills for Chrome 55+ support
import 'core-js/stable'
import 'regenerator-runtime/runtime'

// Additional polyfills for specific features needed by Chrome 55
import 'core-js/features/promise'
import 'core-js/features/array/includes'
import 'core-js/features/array/find'
import 'core-js/features/array/find-index'
import 'core-js/features/object/assign'
import 'core-js/features/object/entries'
import 'core-js/features/object/values'
import 'core-js/features/string/includes'
import 'core-js/features/string/starts-with'
import 'core-js/features/string/ends-with'

// Custom polyfill for fetch if needed (Chrome 55 has basic fetch support)
if (!window.fetch) {
  console.warn('Fetch API not supported, some features may not work properly')
}

// Polyfill for ResizeObserver (not available in Chrome 55)
if (!window.ResizeObserver) {
  window.ResizeObserver = class ResizeObserver {
    constructor(callback) {
      this.callback = callback
      this.entries = []
    }
    
    observe() {
      // Basic fallback - does nothing
    }
    
    unobserve() {
      // Basic fallback - does nothing  
    }
    
    disconnect() {
      // Basic fallback - does nothing
    }
  }
}

// Polyfill for IntersectionObserver (not available in Chrome 55)
if (!window.IntersectionObserver) {
  window.IntersectionObserver = class IntersectionObserver {
    constructor(callback, options = {}) {
      this.callback = callback
      this.options = options
    }
    
    observe() {
      // Basic fallback - does nothing
    }
    
    unobserve() {
      // Basic fallback - does nothing
    }
    
    disconnect() {
      // Basic fallback - does nothing
    }
  }
}