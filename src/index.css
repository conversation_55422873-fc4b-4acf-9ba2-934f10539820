/* 在Tailwind base前先设置防闪烁样式 */
html, body, #root {
  background-color: #000000 !important;
  background: #000000 !important;
  transition: none !important;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Tailwind 背景色重写保护 */
.bg-white {
  background-color: #000000 !important;
}

/* 基础样式重置和防背景闪烁 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* 防止意外的背景色变化 */
  transition: background-color 0s !important;
}

/* 防止背景闪烁 - 确保一致的黑色背景 */
html, body {
  background-color: #000000 !important;
  background: #000000 !important;
  scroll-behavior: smooth;
  /* 防止在加载过程中显示白色背景 */
  color: #ffffff;
  /* 确保立即应用背景色，无过渡 */
  transition: none !important;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 选择文本样式 */
::selection {
  background-color: rgba(6, 182, 212, 0.3);
  color: #fff;
}

/* 防止移动端双击缩放 */
body {
  touch-action: manipulation;
}

/* 确保最小高度和背景一致性 */
#root {
  min-height: 100vh;
  background-color: #000000 !important;
  background: #000000 !important;
  /* 防止任何可能的背景闪烁 */
  transition: none !important;
  /* 确保容器始终填满屏幕 */
  width: 100%;
  position: relative;
}

/* 动画性能优化 */
.will-change-transform {
  will-change: transform;
}

/* 自定义动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 轻量化动画 - 替代framer-motion */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 防闪烁的温和发光动画 */
@keyframes gentle-glow {
  0%, 100% {
    opacity: 0.15;
    transform: scale(1);
  }
  50% {
    opacity: 0.25;
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

/* 新增动画类 */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 性能优化的动画属性 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* GPU 加速的变换 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
}

.animate-gradient {
  animation: gradient 6s ease infinite;
}

/* 防止图标闪烁 */
.material-icons,
.fa {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 内网环境emoji字体支持 */
body, 
html {
  font-family: 
    -apple-system, 
    BlinkMacSystemFont, 
    "Segoe UI", 
    "Roboto", 
    "Helvetica Neue", 
    Arial, 
    "Noto Sans",
    "Liberation Sans", 
    sans-serif, 
    "Apple Color Emoji", 
    "Segoe UI Emoji", 
    "Segoe UI Symbol", 
    "Noto Color Emoji";
}

/* 内网环境下emoji回退方案 */
.emoji-fallback {
  font-family: 
    "Apple Color Emoji", 
    "Segoe UI Emoji", 
    "Noto Color Emoji", 
    "Segoe UI Symbol", 
    "Symbola",
    serif;
}

/* 确保关键emoji在所有环境下都能显示 */
.emoji-rocket::before {
  content: "🚀";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", serif;
}

/* 如果emoji不支持，则使用FontAwesome图标替代 */
@supports not (font-family: "Apple Color Emoji") {
  .emoji-rocket::before {
    content: "";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
  }
}

/* 如果emoji不支持，提供回退方案 */
.emoji-rocket::before {
  content: "🚀";
}

/* 内网环境下可能需要的emoji回退 */
@media (max-width: 600px) {
  .emoji-rocket::before {
    content: "[Rocket]";
  }
}


/* Loading spinner动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* WebIDE 自适应样式优化 */
.webide-container {
  /* 确保在不同屏幕尺寸下都有合适的高度 */
  min-height: 500px;
}

/* 小屏幕设备优化 */
@media (max-width: 640px) {
  .webide-container {
    min-height: 450px;
  }
}

/* 中等屏幕设备优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  .webide-container {
    min-height: 600px;
  }
}

/* 大屏幕设备优化 */
@media (min-width: 1025px) {
  .webide-container {
    min-height: 700px;
  }
}

/* 超大屏幕设备优化 - 使用视口高度 */
@media (min-width: 1536px) {
  .webide-container {
    min-height: calc(100vh - 200px);
    max-height: calc(100vh - 100px);
  }
}

/* 导航栏响应式优化 */
@media (max-width: 768px) {
  /* 小屏幕下导航栏高度调整 */
  .navbar-height {
    height: 64px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* 中等屏幕下的导航栏优化 */
  .navbar-spacing {
    gap: 0.5rem;
  }
}

@media (min-width: 1025px) {
  /* 大屏幕下的导航栏优化 */
  .navbar-spacing {
    gap: 1rem;
  }
}

/* 确保iframe在容器内完全填充 */
.webide-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

/* 优化加载状态的显示 */
.webide-loading {
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

