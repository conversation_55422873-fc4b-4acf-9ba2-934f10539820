/* 性能优化的 CSS 样式 */

/* 根据性能标志动态调整样式 */
:root {
  --animation-duration: 0.3s;
  --transition-duration: 0.2s;
  --shadow-complexity: 3;
}

/* 低性能设备优化 */
.performance-minimal {
  --animation-duration: 0s;
  --transition-duration: 0s;
  --shadow-complexity: 0;
}

.performance-reduced {
  --animation-duration: 0.1s;
  --transition-duration: 0.1s;
  --shadow-complexity: 1;
}

/* GPU 加速优化 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 避免重排的动画 */
.animate-transform-only {
  transition: transform var(--transition-duration) ease-out;
}

.animate-opacity-only {
  transition: opacity var(--transition-duration) ease-out;
}

/* 简化的阴影效果 */
.shadow-optimized {
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
}

.shadow-minimal {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

/* 渐变优化 */
.gradient-optimized {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-simple {
  background: #667eea;
}

/* 字体渲染优化 */
.text-optimized {
  font-display: swap;
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图片优化 */
.img-optimized {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimizeSpeed;
}

/* 滚动优化 */
.scroll-optimized {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  scroll-behavior: auto; /* 在低性能设备上禁用平滑滚动 */
}

/* 低性能设备的简化动画 */
@media (prefers-reduced-motion: reduce) {
  .performance-auto {
    --animation-duration: 0s;
    --transition-duration: 0s;
  }
  
  .animate-pulse {
    animation: none;
  }
  
  .animate-spin {
    animation: none;
  }
  
  .animate-bounce {
    animation: none;
  }
  
  /* 禁用所有可能导致闪烁的动画 */
  [style*="animation"] {
    animation: none !important;
  }
}

/* 性能优化：在低性能模式下禁用背景动画 */
.performance-minimal [style*="gentle-glow"],
.performance-reduced [style*="gentle-glow"] {
  animation: none !important;
  opacity: 0.2 !important;
}

/* CPU 密集型样式的替代方案 */
.blur-alternative {
  opacity: 0.8;
  /* 用透明度替代 backdrop-filter: blur() */
}

.complex-border-alternative {
  border: 2px solid #ccc;
  /* 用简单边框替代复杂的边框效果 */
}

/* 容器查询替代方案（为不支持的浏览器） */
.container-responsive {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (max-width: 768px) {
  .container-responsive {
    padding: 0 0.5rem;
  }
}

/* 低端设备的网格布局简化 */
.grid-optimized {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.grid-fallback {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.grid-fallback > * {
  flex: 1 1 250px;
}

/* 粘性定位的替代方案 */
.sticky-fallback {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

/* 性能监控的样式 */
.performance-debug {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem;
  font-size: 0.75rem;
  border-radius: 4px;
  z-index: 9999;
  font-family: monospace;
}

/* 低性能设备的加载状态 */
.loading-minimal {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
}

/* 只在高性能设备上显示复杂动画 */
.high-performance-only {
  display: none;
}

.performance-high .high-performance-only {
  display: block;
}

/* 内容可见性优化 */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* 层叠上下文优化 */
.isolate-layer {
  isolation: isolate;
}

/* 避免重绘的样式 */
.no-repaint {
  transform: translateZ(0);
  opacity: 0.99; /* 触发硬件加速 */
}

/* 响应式图片容器 */
.responsive-image-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 宽高比 */
  overflow: hidden;
}

.responsive-image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 性能优化的表格 */
.table-optimized {
  table-layout: fixed;
  width: 100%;
}

.table-optimized td,
.table-optimized th {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}