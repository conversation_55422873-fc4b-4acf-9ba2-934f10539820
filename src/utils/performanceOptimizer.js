/**
 * 性能优化器 - 检测设备性能并提供降级方案
 */

class PerformanceOptimizer {
  constructor() {
    this.deviceCapabilities = null;
    this.optimizationLevel = 'auto';
    this.performanceMetrics = {
      initialLoadTime: 0,
      memoryUsage: 0,
      cpuLevel: 'unknown'
    };
    
    this.init();
  }

  /**
   * 初始化性能检测
   */
  init() {
    this.detectDeviceCapabilities();
    this.setupPerformanceMonitoring();
    this.applyOptimizations();
  }

  /**
   * 检测设备性能能力
   */
  detectDeviceCapabilities() {
    const capabilities = {
      memory: this.detectMemoryLevel(),
      cpu: this.detectCPULevel(),
      connection: this.detectConnectionSpeed(),
      browser: this.detectBrowserCapabilities(),
      deviceType: this.detectDeviceType()
    };

    // 综合评分 (0-100)
    const score = this.calculatePerformanceScore(capabilities);
    
    this.deviceCapabilities = {
      ...capabilities,
      score,
      level: this.getPerformanceLevel(score)
    };

    console.log('[PerformanceOptimizer] 设备性能评估:', this.deviceCapabilities);
  }

  /**
   * 检测内存水平
   */
  detectMemoryLevel() {
    if (navigator.deviceMemory) {
      // 基于实际内存大小
      if (navigator.deviceMemory >= 8) return 'high';
      if (navigator.deviceMemory >= 4) return 'medium';
      return 'low';
    }

    // 基于JavaScript堆内存（如果可用）
    if (performance.memory) {
      const limitMB = performance.memory.jsHeapSizeLimit / 1024 / 1024;
      if (limitMB >= 1024) return 'high';
      if (limitMB >= 512) return 'medium';
      return 'low';
    }

    return 'unknown';
  }

  /**
   * 检测CPU性能水平
   */
  detectCPULevel() {
    return new Promise((resolve) => {
      const start = performance.now();
      let iterations = 0;
      const maxTime = 50; // 50ms测试时间

      const testLoop = () => {
        const loopStart = performance.now();
        
        // 执行一些计算密集的操作
        while (performance.now() - loopStart < 5) {
          Math.random() * Math.random();
          iterations++;
        }

        if (performance.now() - start < maxTime) {
          requestAnimationFrame(testLoop);
        } else {
          // 基于迭代次数判断CPU性能
          let level;
          if (iterations > 50000) level = 'high';
          else if (iterations > 25000) level = 'medium';
          else level = 'low';

          this.performanceMetrics.cpuLevel = level;
          resolve(level);
        }
      };

      requestAnimationFrame(testLoop);
    });
  }

  /**
   * 检测网络连接速度
   */
  detectConnectionSpeed() {
    if (navigator.connection) {
      const connection = navigator.connection;
      const effectiveType = connection.effectiveType;
      
      switch (effectiveType) {
        case '4g': return 'fast';
        case '3g': return 'medium';
        case '2g': 
        case 'slow-2g': return 'slow';
        default: return 'unknown';
      }
    }
    
    return 'unknown';
  }

  /**
   * 检测浏览器能力
   */
  detectBrowserCapabilities() {
    const capabilities = {
      webgl: !!window.WebGLRenderingContext,
      webgl2: !!window.WebGL2RenderingContext,
      intersectionObserver: !!window.IntersectionObserver,
      resizeObserver: !!window.ResizeObserver,
      requestIdleCallback: !!window.requestIdleCallback,
      passive: this.supportsPassiveEvents()
    };

    return capabilities;
  }

  /**
   * 检测设备类型
   */
  detectDeviceType() {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/mobile|android|iphone|ipad|phone/i.test(userAgent)) {
      return 'mobile';
    } else if (/tablet|ipad/i.test(userAgent)) {
      return 'tablet';
    }
    
    return 'desktop';
  }

  /**
   * 检测被动事件支持
   */
  supportsPassiveEvents() {
    let supportsPassive = false;
    try {
      const opts = Object.defineProperty({}, 'passive', {
        get() { supportsPassive = true; return true; }
      });
      window.addEventListener('testPassive', null, opts);
      window.removeEventListener('testPassive', null, opts);
    } catch {
      // 忽略被动监听器不支持的错误
    }
    return supportsPassive;
  }

  /**
   * 计算综合性能得分
   */
  calculatePerformanceScore(capabilities) {
    let score = 50; // 基础分数

    // 内存权重 30%
    switch (capabilities.memory) {
      case 'high': score += 30; break;
      case 'medium': score += 15; break;
      case 'low': score -= 15; break;
    }

    // CPU权重 25%
    switch (capabilities.cpu) {
      case 'high': score += 25; break;
      case 'medium': score += 10; break;
      case 'low': score -= 10; break;
    }

    // 网络权重 20%
    switch (capabilities.connection) {
      case 'fast': score += 20; break;
      case 'medium': score += 5; break;
      case 'slow': score -= 10; break;
    }

    // 浏览器能力权重 15%
    const browserScore = Object.values(capabilities.browser).filter(Boolean).length * 2;
    score += Math.min(browserScore, 15);

    // 设备类型权重 10%
    switch (capabilities.deviceType) {
      case 'desktop': score += 10; break;
      case 'tablet': score += 5; break;
      case 'mobile': score -= 5; break;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 根据分数确定性能等级
   */
  getPerformanceLevel(score) {
    if (score >= 80) return 'high';
    if (score >= 60) return 'medium';
    if (score >= 40) return 'low';
    return 'very-low';
  }

  /**
   * 应用性能优化
   */
  applyOptimizations() {
    const level = this.deviceCapabilities?.level || 'medium';
    
    console.log(`[PerformanceOptimizer] 应用${level}级别优化`);
    
    switch (level) {
      case 'very-low':
        this.applyVeryLowEndOptimizations();
        break;
      case 'low':
        this.applyLowEndOptimizations();
        break;
      case 'medium':
        this.applyMediumOptimizations();
        break;
      case 'high':
        this.applyHighEndOptimizations();
        break;
    }

    // 应用通用优化
    this.applyUniversalOptimizations();
  }

  /**
   * 超低端设备优化
   */
  applyVeryLowEndOptimizations() {
    // 禁用非必要动画
    document.documentElement.style.setProperty('--animation-duration', '0s');
    document.documentElement.style.setProperty('--transition-duration', '0s');
    
    // 简化加载动画
    this.setOptimizationFlag('disable-fancy-animations', true);
    this.setOptimizationFlag('reduce-polyfills', true);
    this.setOptimizationFlag('lazy-load-everything', true);
    
    // 禁用图片优化
    this.setOptimizationFlag('disable-image-optimizations', true);
    
    console.log('[PerformanceOptimizer] 应用超低端设备优化');
  }

  /**
   * 低端设备优化
   */
  applyLowEndOptimizations() {
    // 减少动画复杂度
    document.documentElement.style.setProperty('--animation-duration', '0.1s');
    document.documentElement.style.setProperty('--transition-duration', '0.1s');
    
    this.setOptimizationFlag('reduce-animations', true);
    this.setOptimizationFlag('aggressive-lazy-loading', true);
    this.setOptimizationFlag('reduce-bundle-size', true);
    
    console.log('[PerformanceOptimizer] 应用低端设备优化');
  }

  /**
   * 中端设备优化
   */
  applyMediumOptimizations() {
    this.setOptimizationFlag('balanced-performance', true);
    this.setOptimizationFlag('smart-lazy-loading', true);
    
    console.log('[PerformanceOptimizer] 应用中端设备优化');
  }

  /**
   * 高端设备优化
   */
  applyHighEndOptimizations() {
    this.setOptimizationFlag('full-features', true);
    this.setOptimizationFlag('enhanced-animations', true);
    
    console.log('[PerformanceOptimizer] 应用高端设备优化');
  }

  /**
   * 通用优化
   */
  applyUniversalOptimizations() {
    // 预连接关键资源
    this.prefetchCriticalResources();
    
    // 优化字体加载
    this.optimizeFontLoading();
    
    // 设置资源提示
    this.setupResourceHints();
  }

  /**
   * 设置优化标志
   */
  setOptimizationFlag(flag, value) {
    if (!window.PERFORMANCE_FLAGS) {
      window.PERFORMANCE_FLAGS = {};
    }
    window.PERFORMANCE_FLAGS[flag] = value;
  }

  /**
   * 获取优化标志
   */
  getOptimizationFlag(flag) {
    return window.PERFORMANCE_FLAGS?.[flag] || false;
  }

  /**
   * 预连接关键资源
   */
  prefetchCriticalResources() {
    const criticalDomains = [
      // API域名可以在这里添加
    ];

    criticalDomains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = domain;
      document.head.appendChild(link);
    });
  }

  /**
   * 优化字体加载
   */
  optimizeFontLoading() {
    // 使用font-display: swap优化字体加载
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-family: system-ui, -apple-system, sans-serif;
        font-display: swap;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 设置资源提示
   */
  setupResourceHints() {
    // 这个方法可以根据实际需求添加资源预加载
  }

  /**
   * 设置性能监控
   */
  setupPerformanceMonitoring() {
    // 监控长任务
    if (window.PerformanceObserver) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            console.warn('[PerformanceOptimizer] 长任务检测:', {
              duration: entry.duration,
              startTime: entry.startTime
            });
          }
        }
      });
      
      try {
        observer.observe({ entryTypes: ['longtask'] });
      } catch {
        console.warn('[PerformanceOptimizer] 长任务监控不支持');
      }
    }

    // 监控内存使用
    if (performance.memory) {
      setInterval(() => {
        const memory = performance.memory;
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
          console.warn('[PerformanceOptimizer] 内存使用接近限制');
        }
      }, 30000); // 30秒检查一次
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    return {
      deviceCapabilities: this.deviceCapabilities,
      optimizationLevel: this.optimizationLevel,
      performanceMetrics: this.performanceMetrics,
      activeFlags: window.PERFORMANCE_FLAGS || {}
    };
  }
}

// 创建全局实例
const performanceOptimizer = new PerformanceOptimizer();

// 导出
export default performanceOptimizer;