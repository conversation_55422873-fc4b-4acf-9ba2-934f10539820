/**
 * 错误检查工具
 * 用于验证应用启动后是否有JavaScript错误
 */

class ErrorChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.setupErrorHandlers();
  }

  /**
   * 设置错误处理器
   */
  setupErrorHandlers() {
    // 捕获全局JavaScript错误
    window.addEventListener('error', (event) => {
      this.errors.push({
        type: 'JavaScript Error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        timestamp: new Date().toISOString(),
        stack: event.error?.stack
      });
      console.error('[ErrorChe<PERSON>] JavaScript错误:', event.message);
    });

    // 捕获Promise rejection错误
    window.addEventListener('unhandledrejection', (event) => {
      this.errors.push({
        type: 'Unhandled Promise Rejection',
        message: event.reason?.message || event.reason,
        timestamp: new Date().toISOString(),
        stack: event.reason?.stack
      });
      console.error('[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>] Promise rejection:', event.reason);
    });

    // 捕获控制台警告
    const originalWarn = console.warn;
    console.warn = (...args) => {
      this.warnings.push({
        type: 'Console Warning',
        message: args.join(' '),
        timestamp: new Date().toISOString()
      });
      originalWarn.apply(console, args);
    };
  }

  /**
   * 检查特定的错误模式
   */
  checkForSpecificErrors() {
    const specificChecks = [
      {
        name: 'detectCurrentSection错误',
        check: () => {
          // 检查是否有detectCurrentSection相关的错误
          return this.errors.some(error => 
            error.message && error.message.includes('detectCurrentSection')
          );
        }
      },
      {
        name: 'AppPreloader错误',
        check: () => {
          return this.errors.some(error => 
            error.message && error.message.includes('AppPreloader')
          );
        }
      },
      {
        name: 'pageContentCollector错误',
        check: () => {
          return this.errors.some(error => 
            error.message && error.message.includes('pageContentCollector')
          );
        }
      },
    ];

    const results = {};
    specificChecks.forEach(check => {
      results[check.name] = check.check();
    });

    return results;
  }

  /**
   * 获取错误报告
   */
  getErrorReport() {
    const specificErrors = this.checkForSpecificErrors();
    
    return {
      summary: {
        totalErrors: this.errors.length,
        totalWarnings: this.warnings.length,
        hasDetectCurrentSectionError: specificErrors['detectCurrentSection错误'],
        hasAppPreloaderError: specificErrors['AppPreloader错误'],
        hasPageContentCollectorError: specificErrors['pageContentCollector错误'],
      },
      errors: this.errors,
      warnings: this.warnings,
      specificErrors
    };
  }

  /**
   * 打印错误报告
   */
  printReport() {
    const report = this.getErrorReport();
    
    console.log('\n[ErrorChecker] ===== 错误检查报告 =====');
    console.log(`总错误数: ${report.summary.totalErrors}`);
    console.log(`总警告数: ${report.summary.totalWarnings}`);
    
    if (report.summary.totalErrors === 0) {
      console.log('✅ 没有发现JavaScript错误');
    } else {
      console.log('❌ 发现以下错误:');
      report.errors.forEach((error, index) => {
        console.log(`${index + 1}. [${error.type}] ${error.message}`);
        if (error.filename) {
          console.log(`   文件: ${error.filename}:${error.lineno}:${error.colno}`);
        }
      });
    }

    console.log('\n特定错误检查:');
    Object.entries(report.specificErrors).forEach(([name, hasError]) => {
      const icon = hasError ? '❌' : '✅';
      console.log(`${icon} ${name}: ${hasError ? '发现' : '未发现'}`);
    });

    if (report.summary.totalWarnings > 0) {
      console.log('\n⚠️ 警告信息:');
      report.warnings.slice(0, 5).forEach((warning, index) => {
        console.log(`${index + 1}. ${warning.message}`);
      });
      if (report.warnings.length > 5) {
        console.log(`... 还有 ${report.warnings.length - 5} 个警告`);
      }
    }

    console.log('=====================================\n');
    
    return report;
  }

  /**
   * 清除错误记录
   */
  clearErrors() {
    this.errors = [];
    this.warnings = [];
  }

  /**
   * 检查应用健康状态
   */
  checkAppHealth() {
    const report = this.getErrorReport();
    
    // 检查关键服务是否正常
    const healthChecks = {
      appPreloader: typeof window.appPreloader !== 'undefined',
      pageContentCollector: typeof window.pageContentCollector !== 'undefined',
      enhancedDocumentService: typeof window.enhancedDocumentService !== 'undefined'
    };

    return {
      isHealthy: report.summary.totalErrors === 0,
      errorCount: report.summary.totalErrors,
      warningCount: report.summary.totalWarnings,
      services: healthChecks,
      criticalErrors: report.errors.filter(error => 
        error.message && (
          error.message.includes('detectCurrentSection') ||
          error.message.includes('Cannot read properties of undefined')
        )
      )
    };
  }
}

// 创建全局错误检查器实例
const errorChecker = new ErrorChecker();

// 在开发环境中定期检查错误
if (process.env.NODE_ENV === 'development') {
  // 延迟启动错误检查，确保应用完全加载
  setTimeout(() => {
    console.log('[ErrorChecker] 开始错误监控...');
    
    // 5秒后进行第一次检查
    setTimeout(() => {
      const _report = errorChecker.printReport();
      const health = errorChecker.checkAppHealth();
      
      if (health.isHealthy) {
        console.log('🎉 应用健康状态良好！');
      } else {
        console.log('⚠️ 应用存在问题，请检查错误报告');
      }
    }, 5000);
    
    // 每30秒检查一次
    setInterval(() => {
      const health = errorChecker.checkAppHealth();
      if (!health.isHealthy && health.criticalErrors.length > 0) {
        console.log('🚨 检测到关键错误，请立即处理！');
        errorChecker.printReport();
      }
    }, 30000);
  }, 1000);
}

export default errorChecker;
