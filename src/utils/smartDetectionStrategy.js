/**
 * 智能检测策略
 * 基于用户行为和历史数据优化浏览器兼容性检测
 */

// 延迟导入以避免循环依赖
let browserPerformanceMonitor = null;

class SmartDetectionStrategy {
  constructor() {
    this.cache = new Map();
    this.detectionHistory = this.loadDetectionHistory();
    this.adaptiveThresholds = this.calculateAdaptiveThresholds();
    this.loadPerformanceMonitor();
  }

  /**
   * 异步加载性能监控器
   */
  async loadPerformanceMonitor() {
    if (!browserPerformanceMonitor) {
      try {
        const { default: monitor } = await import('./browserPerformanceMonitor.js');
        browserPerformanceMonitor = monitor;
      } catch (error) {
        console.warn('Performance monitor not available:', error);
      }
    }
  }

  /**
   * 加载检测历史
   */
  loadDetectionHistory() {
    try {
      const history = localStorage.getItem('browser-detection-history');
      return history ? JSON.parse(history) : [];
    } catch {
      return [];
    }
  }

  /**
   * 保存检测历史
   */
  saveDetectionHistory() {
    try {
      // 只保留最近50条记录
      const recentHistory = this.detectionHistory.slice(-50);
      localStorage.setItem('browser-detection-history', JSON.stringify(recentHistory));
    } catch (error) {
      console.warn('Failed to save detection history:', error);
    }
  }

  /**
   * 计算自适应阈值
   */
  calculateAdaptiveThresholds() {
    const report = browserPerformanceMonitor?.getReport();
    if (!report) {
      return this.getDefaultThresholds();
    }

    // 基于历史数据调整阈值
    const thresholds = this.getDefaultThresholds();
    
    // 如果检测时间过长，降低检测频率
    if (report.performance.avgDetectionTime > 200) {
      thresholds.detectionInterval *= 1.5;
    }

    // 如果错误率高，增加容错性
    const errorRate = Object.values(report.errors).reduce((a, b) => a + b, 0) / report.totalSessions;
    if (errorRate > 0.1) {
      thresholds.errorTolerance = true;
    }

    return thresholds;
  }

  /**
   * 获取默认阈值
   */
  getDefaultThresholds() {
    return {
      detectionInterval: 24 * 60 * 60 * 1000, // 24小时
      cacheExpiry: 60 * 60 * 1000, // 1小时
      errorTolerance: false,
      skipDetectionForKnownBrowsers: true,
      prioritizePerformance: true
    };
  }

  /**
   * 智能检测决策
   */
  shouldRunDetection(userAgent) {
    const cacheKey = this.generateCacheKey(userAgent);
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.adaptiveThresholds.cacheExpiry) {
        return { shouldRun: false, reason: 'cached', result: cached.result };
      }
    }

    // 检查历史记录
    const recentDetection = this.findRecentDetection(userAgent);
    if (recentDetection && this.adaptiveThresholds.skipDetectionForKnownBrowsers) {
      const timeSinceLastDetection = Date.now() - recentDetection.timestamp;
      if (timeSinceLastDetection < this.adaptiveThresholds.detectionInterval) {
        return { shouldRun: false, reason: 'recent', result: recentDetection.result };
      }
    }

    // 检查是否为已知兼容浏览器
    if (this.isKnownCompatibleBrowser(userAgent)) {
      return { shouldRun: false, reason: 'known-compatible', result: this.getCompatibleResult() };
    }

    return { shouldRun: true, reason: 'required' };
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(userAgent) {
    // 提取关键信息生成简化的键
    const simplified = userAgent
      .toLowerCase()
      .replace(/\d+\.\d+\.\d+/g, 'x.x.x') // 替换版本号
      .replace(/\s+/g, ' ')
      .trim();
    
    return btoa(simplified).substring(0, 16); // Base64编码并截取
  }

  /**
   * 查找最近的检测记录
   */
  findRecentDetection(userAgent) {
    const cacheKey = this.generateCacheKey(userAgent);
    return this.detectionHistory
      .filter(record => record.cacheKey === cacheKey)
      .sort((a, b) => b.timestamp - a.timestamp)[0];
  }

  /**
   * 检查是否为已知兼容浏览器
   */
  isKnownCompatibleBrowser(userAgent) {
    const ua = userAgent.toLowerCase();
    
    // 现代浏览器的最新版本通常是兼容的
    const modernBrowsers = [
      { name: 'chrome', minVersion: 90 },
      { name: 'firefox', minVersion: 85 },
      { name: 'safari', minVersion: 14 },
      { name: 'edge', minVersion: 90 }
    ];

    for (const browser of modernBrowsers) {
      if (ua.includes(browser.name)) {
        const versionMatch = ua.match(new RegExp(`${browser.name}\\/(\\d+)`));
        if (versionMatch && parseInt(versionMatch[1]) >= browser.minVersion) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 获取兼容结果
   */
  getCompatibleResult() {
    return {
      isCompatible: true,
      isRecommendedVersion: true,
      browser: { name: 'Modern Browser', version: 'Latest' },
      features: {},
      integrationCompatibility: {
        OpenVSCode: { compatible: true, issues: [], features: {} },
        LobeChat: { compatible: true, issues: [], features: {} },
        issues: [],
        hasIssues: false
      },
      issues: {
        versionTooOld: false,
        belowRecommended: false,
        unsupportedFeatures: [],
        missingRecommendedFeatures: [],
        integrationIssues: []
      },
      recommendations: []
    };
  }

  /**
   * 记录检测结果
   */
  recordDetectionResult(userAgent, result, duration) {
    const cacheKey = this.generateCacheKey(userAgent);
    const record = {
      cacheKey,
      userAgent,
      result,
      duration,
      timestamp: Date.now()
    };

    // 更新缓存
    this.cache.set(cacheKey, record);

    // 更新历史记录
    this.detectionHistory.push(record);
    this.saveDetectionHistory();

    // 记录到性能监控
    browserPerformanceMonitor.recordCompatibilityResult(result);
  }

  /**
   * 优化检测流程
   */
  optimizeDetection(detectionFunction) {
    return async (userAgent) => {
      const startTime = performance.now();
      
      // 智能决策
      const decision = this.shouldRunDetection(userAgent);
      if (!decision.shouldRun) {
        console.log(`[SmartDetection] Skipping detection: ${decision.reason}`);
        return decision.result;
      }

      try {
        // 执行检测
        console.log('[SmartDetection] Running full detection');
        const result = await detectionFunction();
        
        const duration = performance.now() - startTime;
        this.recordDetectionResult(userAgent, result, duration);
        
        return result;
      } catch (error) {
        console.error('[SmartDetection] Detection failed:', error);
        
        // 错误容错处理
        if (this.adaptiveThresholds.errorTolerance) {
          return this.getCompatibleResult();
        }
        
        throw error;
      }
    };
  }

  /**
   * 获取检测统计
   */
  getStatistics() {
    const report = browserPerformanceMonitor.getReport();
    return {
      cacheHitRate: this.calculateCacheHitRate(),
      avgDetectionTime: report?.performance.avgDetectionTime || 0,
      totalDetections: this.detectionHistory.length,
      recentDetections: this.detectionHistory.filter(
        record => Date.now() - record.timestamp < 7 * 24 * 60 * 60 * 1000
      ).length,
      adaptiveThresholds: this.adaptiveThresholds,
      browserDistribution: this.getBrowserDistribution()
    };
  }

  /**
   * 计算缓存命中率
   */
  calculateCacheHitRate() {
    const recentHistory = this.detectionHistory.filter(
      record => Date.now() - record.timestamp < 7 * 24 * 60 * 60 * 1000
    );
    
    if (recentHistory.length === 0) return 0;
    
    const uniqueBrowsers = new Set(recentHistory.map(r => r.cacheKey));
    return (recentHistory.length - uniqueBrowsers.size) / recentHistory.length;
  }

  /**
   * 获取浏览器分布
   */
  getBrowserDistribution() {
    const distribution = {};
    this.detectionHistory.forEach(record => {
      const browserName = record.result?.browser?.name || 'Unknown';
      distribution[browserName] = (distribution[browserName] || 0) + 1;
    });
    return distribution;
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
    this.detectionHistory = [];
    this.saveDetectionHistory();
    console.log('[SmartDetection] Cache cleared');
  }

  /**
   * 重新计算阈值
   */
  recalculateThresholds() {
    this.adaptiveThresholds = this.calculateAdaptiveThresholds();
    console.log('[SmartDetection] Thresholds recalculated:', this.adaptiveThresholds);
  }
}

// 创建全局实例
const smartDetectionStrategy = new SmartDetectionStrategy();

export default smartDetectionStrategy;
export { SmartDetectionStrategy };
