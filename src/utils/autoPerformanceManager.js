/**
 * 自动性能管理器 - 完全自动化的性能优化
 * 无需用户干预，系统自动检测并应用最佳性能策略
 */

class AutoPerformanceManager {
  constructor() {
    this.currentOptimizationLevel = 'detecting';
    this.deviceCapabilities = null;
    this.performanceMetrics = {
      fps: 60,
      memoryPressure: 'normal',
      loadTime: 0,
      networkSpeed: 'unknown'
    };
    this.optimizationHistory = [];
    this.isInitialized = false;
    
    this.init();
  }

  /**
   * 初始化自动性能管理
   */
  async init() {
    console.log('[AutoPerformanceManager] 启动自动性能检测...');
    
    // 立即应用预防性优化
    this.applyPreventiveOptimizations();
    
    // 异步检测设备性能
    await this.detectDevicePerformance();
    
    // 基于检测结果自动优化
    this.autoOptimize();
    
    // 启动实时监控
    this.startRealTimeMonitoring();
    
    this.isInitialized = true;
    console.log(`[AutoPerformanceManager] 自动优化完成，应用级别: ${this.currentOptimizationLevel}`);
  }

  /**
   * 预防性优化 - 立即应用，不需要等待检测
   */
  applyPreventiveOptimizations() {
    // 1. 禁用用户无法察觉但消耗性能的特效
    if (!window.requestIdleCallback) {
      // 老浏览器立即禁用复杂动画
      this.setOptimizationFlag('disable-idle-callbacks', true);
    }

    // 2. 基于用户代理的预判优化
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes('mobile') || userAgent.includes('android')) {
      this.setOptimizationFlag('mobile-optimizations', true);
    }

    // 3. 内存限制的预防措施
    if (navigator.deviceMemory && navigator.deviceMemory <= 2) {
      this.setOptimizationFlag('low-memory-mode', true);
    }

    // 4. 连接速度的预判
    if (navigator.connection) {
      const connection = navigator.connection;
      if (['2g', 'slow-2g'].includes(connection.effectiveType)) {
        this.setOptimizationFlag('slow-network-mode', true);
      }
    }
  }

  /**
   * 设备性能检测 - 多维度评估
   */
  async detectDevicePerformance() {
    const results = await Promise.all([
      this.measureCPUPerformance(),
      this.measureMemoryCapability(),
      this.measureRenderingPerformance(),
      this.measureNetworkCapability(),
      this.detectBrowserCapabilities()
    ]);

    this.deviceCapabilities = {
      cpu: results[0],
      memory: results[1],
      rendering: results[2],
      network: results[3],
      browser: results[4],
      overallScore: this.calculateOverallScore(results)
    };

    return this.deviceCapabilities;
  }

  /**
   * CPU性能测试
   */
  measureCPUPerformance() {
    return new Promise((resolve) => {
      const start = performance.now();
      let operations = 0;
      const testDuration = 50; // 50ms测试

      const cpuTest = () => {
        const iterStart = performance.now();
        
        // 执行计算密集任务
        while (performance.now() - iterStart < 5) {
          Math.random() * Math.random();
          operations++;
        }

        if (performance.now() - start < testDuration) {
          requestAnimationFrame(cpuTest);
        } else {
          // 根据操作数量评估CPU性能
          const score = Math.min(100, operations / 1000); // 标准化到100
          resolve({
            score,
            operations,
            level: score > 70 ? 'high' : score > 40 ? 'medium' : 'low'
          });
        }
      };

      requestAnimationFrame(cpuTest);
    });
  }

  /**
   * 内存能力评估
   */
  measureMemoryCapability() {
    const capability = {
      deviceMemory: navigator.deviceMemory || 0,
      jsHeapLimit: performance.memory ? performance.memory.jsHeapSizeLimit / 1024 / 1024 : 0,
      level: 'unknown'
    };

    // 综合评估内存能力
    if (capability.deviceMemory >= 8 || capability.jsHeapLimit >= 1024) {
      capability.level = 'high';
      capability.score = 90;
    } else if (capability.deviceMemory >= 4 || capability.jsHeapLimit >= 512) {
      capability.level = 'medium';  
      capability.score = 60;
    } else if (capability.deviceMemory >= 2 || capability.jsHeapLimit >= 256) {
      capability.level = 'low';
      capability.score = 30;
    } else {
      capability.level = 'very-low';
      capability.score = 10;
    }

    return capability;
  }

  /**
   * 渲染性能测试
   */
  measureRenderingPerformance() {
    return new Promise((resolve) => {
      let frameCount = 0;
      let lastTime = performance.now();
      let totalFrameTime = 0;

      const measureFrames = () => {
        const currentTime = performance.now();
        const frameTime = currentTime - lastTime;
        totalFrameTime += frameTime;
        frameCount++;
        lastTime = currentTime;

        if (frameCount < 30) { // 测试30帧
          requestAnimationFrame(measureFrames);
        } else {
          const avgFrameTime = totalFrameTime / frameCount;
          const fps = 1000 / avgFrameTime;
          
          resolve({
            fps: Math.round(fps),
            avgFrameTime,
            level: fps > 55 ? 'high' : fps > 30 ? 'medium' : 'low',
            score: Math.min(100, fps * 1.6) // 标准化到100
          });
        }
      };

      requestAnimationFrame(measureFrames);
    });
  }

  /**
   * 网络能力检测
   */
  measureNetworkCapability() {
    const network = {
      connection: navigator.connection,
      level: 'unknown',
      score: 50
    };

    if (navigator.connection) {
      const conn = navigator.connection;
      switch (conn.effectiveType) {
        case '4g':
          network.level = 'fast';
          network.score = 90;
          break;
        case '3g':
          network.level = 'medium';
          network.score = 60;
          break;
        case '2g':
          network.level = 'slow';
          network.score = 30;
          break;
        case 'slow-2g':
          network.level = 'very-slow';
          network.score = 10;
          break;
        default:
          network.level = 'unknown';
          network.score = 50;
      }
    }

    return network;
  }

  /**
   * 浏览器能力检测
   */
  detectBrowserCapabilities() {
    const capabilities = {
      webgl: !!window.WebGLRenderingContext,
      webgl2: !!window.WebGL2RenderingContext,
      intersectionObserver: !!window.IntersectionObserver,
      resizeObserver: !!window.ResizeObserver,
      requestIdleCallback: !!window.requestIdleCallback,
      serviceWorker: 'serviceWorker' in navigator,
      webAssembly: typeof WebAssembly === 'object',
      es6: (() => {
        try {
          new Function('class Test {}; const arrow = () => {};');
          return true;
        } catch {
          return false;
        }
      })()
    };

    const supportCount = Object.values(capabilities).filter(Boolean).length;
    const totalFeatures = Object.keys(capabilities).length;

    return {
      ...capabilities,
      score: (supportCount / totalFeatures) * 100,
      level: supportCount / totalFeatures > 0.8 ? 'modern' : 
             supportCount / totalFeatures > 0.5 ? 'decent' : 'legacy'
    };
  }

  /**
   * 计算综合性能得分
   */
  calculateOverallScore(results) {
    const [cpu, memory, rendering, network, browser] = results;
    
    // 加权计算
    const weights = {
      cpu: 0.25,      // CPU性能权重25%
      memory: 0.3,    // 内存权重30%
      rendering: 0.2, // 渲染权重20%
      network: 0.15,  // 网络权重15%
      browser: 0.1    // 浏览器能力权重10%
    };

    const totalScore = 
      (cpu.score || 50) * weights.cpu +
      (memory.score || 50) * weights.memory +
      (rendering.score || 50) * weights.rendering +
      (network.score || 50) * weights.network +
      (browser.score || 50) * weights.browser;

    return Math.round(totalScore);
  }

  /**
   * 自动优化 - 基于检测结果应用优化
   */
  autoOptimize() {
    const score = this.deviceCapabilities.overallScore;
    let optimizationLevel;

    // 自动确定优化级别
    if (score >= 80) {
      optimizationLevel = 'minimal'; // 高性能设备，最少优化
    } else if (score >= 60) {
      optimizationLevel = 'balanced'; // 中等性能，平衡优化
    } else if (score >= 35) {
      optimizationLevel = 'aggressive'; // 低性能，积极优化
    } else {
      optimizationLevel = 'maximum'; // 超低性能，最大优化
    }

    this.currentOptimizationLevel = optimizationLevel;
    this.applyOptimizationLevel(optimizationLevel);
    
    // 记录优化历史
    this.optimizationHistory.push({
      timestamp: Date.now(),
      level: optimizationLevel,
      score: score,
      reason: 'initial-detection'
    });
  }

  /**
   * 应用指定的优化级别
   */
  applyOptimizationLevel(level) {
    // 清除之前的优化标志
    window.PERFORMANCE_FLAGS = {};

    switch (level) {
      case 'minimal':
        this.applyMinimalOptimizations();
        break;
      case 'balanced':
        this.applyBalancedOptimizations();
        break;
      case 'aggressive':
        this.applyAggressiveOptimizations();
        break;
      case 'maximum':
        this.applyMaximumOptimizations();
        break;
    }

    // 应用CSS类到body
    document.body.className = document.body.className.replace(/auto-perf-\w+/g, '');
    document.body.classList.add(`auto-perf-${level}`);

    console.log(`[AutoPerformanceManager] 应用 ${level} 级别优化`);
  }

  /**
   * 最小优化 - 高性能设备
   */
  applyMinimalOptimizations() {
    this.setOptimizationFlag('full-features', true);
    this.setOptimizationFlag('enhanced-animations', true);
    // 高性能设备可以使用所有功能
  }

  /**
   * 平衡优化 - 中等性能设备
   */
  applyBalancedOptimizations() {
    this.setOptimizationFlag('balanced-mode', true);
    this.setOptimizationFlag('smart-loading', true);
    
    // 适度减少复杂效果
    this.applyCSSOptimizations({
      animationDuration: '0.2s',
      transitionDuration: '0.15s'
    });
  }

  /**
   * 积极优化 - 低性能设备
   */
  applyAggressiveOptimizations() {
    this.setOptimizationFlag('reduce-animations', true);
    this.setOptimizationFlag('lazy-load-heavy', true);
    this.setOptimizationFlag('simplify-effects', true);

    this.applyCSSOptimizations({
      animationDuration: '0.1s',
      transitionDuration: '0.1s'
    });

    // 禁用复杂的视觉效果
    this.disableComplexEffects();
  }

  /**
   * 最大优化 - 超低性能设备
   */
  applyMaximumOptimizations() {
    this.setOptimizationFlag('disable-animations', true);
    this.setOptimizationFlag('minimal-ui', true);
    this.setOptimizationFlag('text-only-fallbacks', true);
    this.setOptimizationFlag('emergency-mode', true);

    this.applyCSSOptimizations({
      animationDuration: '0s',
      transitionDuration: '0s'
    });

    // 启用紧急模式 - 最简化的UI
    this.enableEmergencyMode();
  }

  /**
   * 应用CSS优化
   */
  applyCSSOptimizations(settings) {
    const style = document.createElement('style');
    style.id = 'auto-performance-optimizations';
    
    // 移除已有的优化样式
    const existingStyle = document.getElementById('auto-performance-optimizations');
    if (existingStyle) {
      existingStyle.remove();
    }

    style.textContent = `
      :root {
        --auto-animation-duration: ${settings.animationDuration};
        --auto-transition-duration: ${settings.transitionDuration};
      }
      
      * {
        animation-duration: var(--auto-animation-duration) !important;
        transition-duration: var(--auto-transition-duration) !important;
      }
      
      .auto-perf-aggressive .animate-pulse,
      .auto-perf-maximum .animate-pulse,
      .auto-perf-aggressive .animate-spin,
      .auto-perf-maximum .animate-spin {
        animation: none !important;
      }
      
      .auto-perf-maximum * {
        box-shadow: none !important;
        text-shadow: none !important;
        filter: none !important;
      }
    `;
    
    document.head.appendChild(style);
  }

  /**
   * 禁用复杂效果
   */
  disableComplexEffects() {
    // 禁用复杂的渐变背景
    document.querySelectorAll('[class*="gradient"]').forEach(el => {
      if (el.classList.contains('bg-gradient-to')) {
        el.style.background = '#1f2937'; // 替换为简单的深色背景
      }
    });
  }

  /**
   * 启用紧急模式
   */
  enableEmergencyMode() {
    console.log('[AutoPerformanceManager] 启用紧急优化模式');
    
    // 替换复杂组件为简单版本
    this.setOptimizationFlag('emergency-loader', true);
    
    // 禁用所有非必要功能
    const emergencyStyle = document.createElement('style');
    emergencyStyle.textContent = `
      .auto-perf-maximum {
        font-family: system-ui, sans-serif !important;
      }
      
      .auto-perf-maximum * {
        animation: none !important;
        transition: none !important;
        transform: none !important;
        background-image: none !important;
      }
      
      .auto-perf-maximum img {
        display: none !important;
      }
      
      .auto-perf-maximum::after {
        content: " [简化模式]";
        font-size: 0.8em;
        color: #888;
      }
    `;
    
    document.head.appendChild(emergencyStyle);
  }

  /**
   * 实时监控和动态调整
   */
  startRealTimeMonitoring() {
    // FPS监控
    this.monitorFPS();
    
    // 内存压力监控
    this.monitorMemoryPressure();
    
    // 网络状态监控
    this.monitorNetworkChanges();
    
    // 定期重新评估
    setInterval(() => {
      this.reassessPerformance();
    }, 30000); // 每30秒重新评估一次
  }

  /**
   * FPS监控
   */
  monitorFPS() {
    let frameCount = 0;
    let lastTime = performance.now();

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        this.performanceMetrics.fps = fps;
        
        // 如果FPS持续很低，自动升级优化
        if (fps < 20 && this.currentOptimizationLevel !== 'maximum') {
          this.autoUpgradeOptimization('low-fps');
        }
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFPS);
    };

    requestAnimationFrame(measureFPS);
  }

  /**
   * 内存压力监控
   */
  monitorMemoryPressure() {
    if (!performance.memory) return;

    setInterval(() => {
      const memory = performance.memory;
      const usage = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
      
      if (usage > 0.85) {
        this.performanceMetrics.memoryPressure = 'high';
        this.autoUpgradeOptimization('high-memory-usage');
      } else if (usage > 0.7) {
        this.performanceMetrics.memoryPressure = 'medium';
      } else {
        this.performanceMetrics.memoryPressure = 'normal';
      }
    }, 5000);
  }

  /**
   * 网络状态监控
   */
  monitorNetworkChanges() {
    if (!navigator.connection) return;

    navigator.connection.addEventListener('change', () => {
      const connection = navigator.connection;
      if (['2g', 'slow-2g'].includes(connection.effectiveType)) {
        this.autoUpgradeOptimization('slow-network');
      }
    });
  }

  /**
   * 自动升级优化级别
   */
  autoUpgradeOptimization(reason) {
    const levelOrder = ['minimal', 'balanced', 'aggressive', 'maximum'];
    const currentIndex = levelOrder.indexOf(this.currentOptimizationLevel);
    
    if (currentIndex < levelOrder.length - 1) {
      const newLevel = levelOrder[currentIndex + 1];
      
      console.log(`[AutoPerformanceManager] 自动升级优化: ${this.currentOptimizationLevel} -> ${newLevel} (原因: ${reason})`);
      
      this.currentOptimizationLevel = newLevel;
      this.applyOptimizationLevel(newLevel);
      
      this.optimizationHistory.push({
        timestamp: Date.now(),
        level: newLevel,
        reason: reason,
        automatic: true
      });
    }
  }

  /**
   * 重新评估性能
   */
  async reassessPerformance() {
    // 只有在检测到性能问题时才重新评估
    if (this.performanceMetrics.fps > 30 && 
        this.performanceMetrics.memoryPressure === 'normal') {
      return;
    }

    console.log('[AutoPerformanceManager] 重新评估设备性能...');
    
    await this.detectDevicePerformance();
    this.autoOptimize();
  }

  /**
   * 设置优化标志
   */
  setOptimizationFlag(flag, value) {
    if (!window.PERFORMANCE_FLAGS) {
      window.PERFORMANCE_FLAGS = {};
    }
    window.PERFORMANCE_FLAGS[flag] = value;
  }

  /**
   * 获取当前性能状态
   */
  getPerformanceStatus() {
    return {
      currentLevel: this.currentOptimizationLevel,
      deviceCapabilities: this.deviceCapabilities,
      performanceMetrics: this.performanceMetrics,
      optimizationHistory: this.optimizationHistory,
      activeFlags: window.PERFORMANCE_FLAGS || {}
    };
  }
}

// 创建全局单例并自动启动
const autoPerformanceManager = new AutoPerformanceManager();

// 导出到全局用于调试（但不供用户手动控制）
if (process.env.NODE_ENV === 'development') {
  window.autoPerformanceManager = autoPerformanceManager;
}

export default autoPerformanceManager;