/**
 * 快速性能修复 - 立即可应用的优化措施
 */

class QuickPerformanceFix {
  constructor() {
    this.init();
  }

  init() {
    // 立即应用性能修复
    this.detectAndApplyFixes();
  }

  detectAndApplyFixes() {
    console.log('[QuickPerformanceFix] 应用快速性能修复...');

    // 1. 检测并处理低性能设备
    if (this.isLowPerformanceDevice()) {
      this.applyLowPerformanceFixes();
    }

    // 2. 优化图片加载
    this.optimizeImageLoading();

    // 3. 优化字体加载
    this.optimizeFontLoading();

    // 4. 减少不必要的重排
    this.reduceReflows();

    // 5. 优化滚动性能
    this.optimizeScrolling();

    // 6. 内存优化
    this.optimizeMemoryUsage();

    console.log('[QuickPerformanceFix] 性能修复应用完成');
  }

  /**
   * 检测低性能设备
   */
  isLowPerformanceDevice() {
    // 内存检测
    if (navigator.deviceMemory && navigator.deviceMemory <= 2) {
      return true;
    }

    // 网络连接检测
    if (navigator.connection) {
      const connection = navigator.connection;
      if (connection.effectiveType === '2g' || connection.effectiveType === 'slow-2g') {
        return true;
      }
    }

    // CPU检测（简单的计算测试）
    const start = performance.now();
    let count = 0;
    while (performance.now() - start < 10) { // 10ms测试
      count++;
    }
    
    // 如果10ms内计算次数很少，认为是低性能设备
    if (count < 10000) {
      return true;
    }

    // 用户代理检测
    const ua = navigator.userAgent.toLowerCase();
    if (ua.includes('mobile') && (ua.includes('android 4') || ua.includes('android 5'))) {
      return true;
    }

    return false;
  }

  /**
   * 应用低性能设备修复
   */
  applyLowPerformanceFixes() {
    console.log('[QuickPerformanceFix] 应用低性能设备优化');

    // 设置性能标志
    window.PERFORMANCE_FLAGS = {
      ...window.PERFORMANCE_FLAGS,
      'low-performance-device': true,
      'reduce-animations': true,
      'disable-heavy-effects': true
    };

    // 添加CSS类到body
    document.body.classList.add('low-performance-mode');

    // 禁用复杂动画
    const style = document.createElement('style');
    style.textContent = `
      .low-performance-mode * {
        animation-duration: 0.1s !important;
        transition-duration: 0.1s !important;
      }
      .low-performance-mode .animate-pulse,
      .low-performance-mode .animate-spin,
      .low-performance-mode .animate-bounce {
        animation: none !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 优化图片加载
   */
  optimizeImageLoading() {
    // 添加图片懒加载
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
            }
            imageObserver.unobserve(img);
          }
        });
      });

      // 观察所有懒加载图片
      setTimeout(() => {
        document.querySelectorAll('img[data-src]').forEach(img => {
          imageObserver.observe(img);
        });
      }, 1000);
    }

    // 优化现有图片
    document.querySelectorAll('img').forEach(img => {
      // 添加图片优化属性
      img.loading = 'lazy';
      img.decoding = 'async';
      
      // 错误处理
      img.onerror = function() {
        this.style.display = 'none';
      };
    });
  }

  /**
   * 优化字体加载
   */
  optimizeFontLoading() {
    // 使用font-display: swap优化字体显示
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-display: swap;
      }
      * {
        font-display: swap;
      }
    `;
    document.head.appendChild(style);

    // 预加载关键字体
    if (document.fonts && document.fonts.load) {
      document.fonts.load('16px system-ui').catch(() => {
        // 字体加载失败时使用系统字体
        document.body.style.fontFamily = 'system-ui, -apple-system, sans-serif';
      });
    }
  }

  /**
   * 减少重排
   */
  reduceReflows() {
    // 批量DOM操作
    const originalAppendChild = Element.prototype.appendChild;
    Element.prototype.appendChild = function(child) {
      // 使用DocumentFragment来批量插入
      if (!this._pendingChildren) {
        this._pendingChildren = document.createDocumentFragment();
        requestAnimationFrame(() => {
          originalAppendChild.call(this, this._pendingChildren);
          this._pendingChildren = null;
        });
      }
      
      this._pendingChildren.appendChild(child);
      return child;
    };
  }

  /**
   * 优化滚动性能
   */
  optimizeScrolling() {
    // 添加 CSS scroll-behavior 优化
    document.documentElement.style.scrollBehavior = 'auto';
    
    // 优化滚动事件处理
    let scrollTimer;
    const optimizeScrollHandler = (originalHandler) => {
      return function(event) {
        if (scrollTimer) return;
        
        scrollTimer = requestAnimationFrame(() => {
          originalHandler.call(this, event);
          scrollTimer = null;
        });
      };
    };

    // 为现有滚动监听器添加节流
    const originalAddEventListener = Element.prototype.addEventListener;
    Element.prototype.addEventListener = function(type, listener, options) {
      if (type === 'scroll' && typeof listener === 'function') {
        listener = optimizeScrollHandler(listener);
      }
      return originalAddEventListener.call(this, type, listener, options);
    };
  }

  /**
   * 内存优化
   */
  optimizeMemoryUsage() {
    // 定期清理未使用的对象
    setInterval(() => {
      // 清理可能的内存泄漏
      if (window.gc) {
        window.gc();
      }
      
      // 检查内存使用
      if (performance.memory) {
        const memoryUsage = performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit;
        if (memoryUsage > 0.8) {
          console.warn('[QuickPerformanceFix] 内存使用过高:', Math.round(memoryUsage * 100) + '%');
          
          // 触发内存清理
          this.triggerMemoryCleanup();
        }
      }
    }, 30000); // 30秒检查一次

    // 页面隐藏时清理资源
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.triggerMemoryCleanup();
      }
    });
  }

  /**
   * 触发内存清理
   */
  triggerMemoryCleanup() {
    // 清理事件监听器
    document.querySelectorAll('*').forEach(element => {
      if (element._listeners) {
        element._listeners = null;
      }
    });

    // 清理定时器（需要应用自己管理）
    if (window.APP_TIMERS) {
      window.APP_TIMERS.forEach(id => clearInterval(id));
      window.APP_TIMERS = [];
    }

    // 强制垃圾回收（如果可用）
    if (window.gc) {
      window.gc();
    }

    console.log('[QuickPerformanceFix] 内存清理完成');
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    return {
      isLowPerformanceDevice: this.isLowPerformanceDevice(),
      memoryUsage: performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
      } : null,
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : null,
      deviceMemory: navigator.deviceMemory || 'unknown',
      appliedOptimizations: window.PERFORMANCE_FLAGS || {}
    };
  }
}

// 创建实例并导出
const quickPerformanceFix = new QuickPerformanceFix();

// 导出到全局作为调试工具
window.quickPerformanceFix = quickPerformanceFix;

export default quickPerformanceFix;