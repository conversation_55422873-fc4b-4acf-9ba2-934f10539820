/**
 * 浏览器性能监控工具
 * 用于收集浏览器兼容性和性能数据，帮助优化检测策略
 */

class BrowserPerformanceMonitor {
  constructor() {
    this.metrics = {
      compatibility: {},
      performance: {},
      errors: [],
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    };
    
    this.isEnabled = this.checkIfEnabled();
    this.init();
  }

  /**
   * 检查是否启用监控
   */
  checkIfEnabled() {
    // 在开发环境或明确启用时才收集数据
    return process.env.NODE_ENV === 'development' || 
           localStorage.getItem('browser-monitoring-enabled') === 'true';
  }

  /**
   * 初始化监控
   */
  init() {
    if (!this.isEnabled) return;

    this.collectBasicMetrics();
    this.setupErrorHandling();
    this.setupPerformanceObserver();
  }

  /**
   * 收集基础指标
   */
  collectBasicMetrics() {
    try {
      // 浏览器信息
      this.metrics.browser = {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        hardwareConcurrency: navigator.hardwareConcurrency || 'unknown'
      };

      // 屏幕信息
      this.metrics.screen = {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
        pixelDepth: screen.pixelDepth,
        devicePixelRatio: window.devicePixelRatio || 1
      };

      // 视口信息
      this.metrics.viewport = {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollX: window.scrollX,
        scrollY: window.scrollY
      };

      // 内存信息（如果可用）
      if (performance.memory) {
        this.metrics.memory = {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
        };
      }

    } catch (error) {
      this.recordError('collectBasicMetrics', error);
    }
  }

  /**
   * 设置错误处理
   */
  setupErrorHandling() {
    // 全局错误监听
    window.addEventListener('error', (event) => {
      this.recordError('global', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });

    // Promise 错误监听
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError('promise', {
        reason: event.reason,
        stack: event.reason?.stack
      });
    });
  }

  /**
   * 设置性能观察器
   */
  setupPerformanceObserver() {
    if (!('PerformanceObserver' in window)) return;

    try {
      // 观察导航性能
      const navObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'navigation') {
            this.metrics.navigation = {
              domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
              loadComplete: entry.loadEventEnd - entry.loadEventStart,
              domInteractive: entry.domInteractive - entry.navigationStart,
              firstPaint: entry.responseEnd - entry.requestStart
            };
          }
        });
      });
      navObserver.observe({ entryTypes: ['navigation'] });

      // 观察资源加载性能
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        this.metrics.resources = entries.map(entry => ({
          name: entry.name,
          duration: entry.duration,
          size: entry.transferSize || 0,
          type: entry.initiatorType
        }));
      });
      resourceObserver.observe({ entryTypes: ['resource'] });

    } catch (error) {
      this.recordError('setupPerformanceObserver', error);
    }
  }

  /**
   * 记录兼容性检测结果
   */
  recordCompatibilityResult(result) {
    if (!this.isEnabled) return;

    this.metrics.compatibility = {
      ...result,
      timestamp: Date.now(),
      detectionDuration: this.getDetectionDuration()
    };

    this.saveMetrics();
  }

  /**
   * 记录错误
   */
  recordError(source, error) {
    if (!this.isEnabled) return;

    this.metrics.errors.push({
      source,
      error: typeof error === 'string' ? error : error.message || 'Unknown error',
      stack: error.stack,
      timestamp: Date.now()
    });

    // 限制错误记录数量
    if (this.metrics.errors.length > 50) {
      this.metrics.errors = this.metrics.errors.slice(-25);
    }
  }

  /**
   * 获取检测耗时
   */
  getDetectionDuration() {
    if (performance.mark) {
      try {
        performance.mark('compatibility-check-end');
        performance.measure('compatibility-check', 'compatibility-check-start', 'compatibility-check-end');
        const measure = performance.getEntriesByName('compatibility-check')[0];
        return measure ? measure.duration : 0;
      } catch {
        return 0;
      }
    }
    return 0;
  }

  /**
   * 保存指标数据
   */
  saveMetrics() {
    try {
      // 保存到本地存储（仅开发环境）
      if (this.isEnabled && localStorage) {
        const key = `browser-metrics-${Date.now()}`;
        localStorage.setItem(key, JSON.stringify(this.metrics));
        
        // 清理旧数据，只保留最近10条记录
        this.cleanupOldMetrics();
      }
    } catch (error) {
      console.warn('Failed to save browser metrics:', error);
    }
  }

  /**
   * 清理旧的指标数据
   */
  cleanupOldMetrics() {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('browser-metrics-'));
      if (keys.length > 10) {
        // 按时间戳排序，删除最旧的记录
        keys.sort().slice(0, keys.length - 10).forEach(key => {
          localStorage.removeItem(key);
        });
      }
    } catch (error) {
      console.warn('Failed to cleanup old metrics:', error);
    }
  }

  /**
   * 获取统计报告
   */
  getReport() {
    if (!this.isEnabled) return null;

    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('browser-metrics-'));
      const allMetrics = keys.map(key => {
        try {
          return JSON.parse(localStorage.getItem(key));
        } catch {
          return null;
        }
      }).filter(Boolean);

      return {
        totalSessions: allMetrics.length,
        browsers: this.aggregateBrowserStats(allMetrics),
        errors: this.aggregateErrorStats(allMetrics),
        performance: this.aggregatePerformanceStats(allMetrics),
        compatibility: this.aggregateCompatibilityStats(allMetrics)
      };
    } catch (error) {
      console.warn('Failed to generate report:', error);
      return null;
    }
  }

  /**
   * 聚合浏览器统计
   */
  aggregateBrowserStats(metrics) {
    const browsers = {};
    metrics.forEach(metric => {
      if (metric.compatibility?.browser?.name) {
        const name = metric.compatibility.browser.name;
        const version = metric.compatibility.browser.version;
        const key = `${name} ${version}`;
        browsers[key] = (browsers[key] || 0) + 1;
      }
    });
    return browsers;
  }

  /**
   * 聚合错误统计
   */
  aggregateErrorStats(metrics) {
    const errors = {};
    metrics.forEach(metric => {
      if (metric.errors?.length) {
        metric.errors.forEach(error => {
          const key = error.source;
          errors[key] = (errors[key] || 0) + 1;
        });
      }
    });
    return errors;
  }

  /**
   * 聚合性能统计
   */
  aggregatePerformanceStats(metrics) {
    const performance = {
      avgDetectionTime: 0,
      avgLoadTime: 0,
      count: 0
    };

    metrics.forEach(metric => {
      if (metric.compatibility?.detectionDuration) {
        performance.avgDetectionTime += metric.compatibility.detectionDuration;
        performance.count++;
      }
      if (metric.navigation?.loadComplete) {
        performance.avgLoadTime += metric.navigation.loadComplete;
      }
    });

    if (performance.count > 0) {
      performance.avgDetectionTime /= performance.count;
      performance.avgLoadTime /= performance.count;
    }

    return performance;
  }

  /**
   * 聚合兼容性统计
   */
  aggregateCompatibilityStats(metrics) {
    const stats = {
      compatible: 0,
      incompatible: 0,
      warnings: 0,
      total: 0
    };

    metrics.forEach(metric => {
      if (metric.compatibility) {
        stats.total++;
        if (metric.compatibility.isCompatible === false) {
          stats.incompatible++;
        } else if (metric.compatibility.isRecommendedVersion === false) {
          stats.warnings++;
        } else {
          stats.compatible++;
        }
      }
    });

    return stats;
  }

  /**
   * 启用监控
   */
  static enable() {
    localStorage.setItem('browser-monitoring-enabled', 'true');
    console.log('Browser performance monitoring enabled');
  }

  /**
   * 禁用监控
   */
  static disable() {
    localStorage.removeItem('browser-monitoring-enabled');
    console.log('Browser performance monitoring disabled');
  }

  /**
   * 清除所有监控数据
   */
  static clearData() {
    const keys = Object.keys(localStorage).filter(key => key.startsWith('browser-metrics-'));
    keys.forEach(key => localStorage.removeItem(key));
    console.log(`Cleared ${keys.length} browser metrics records`);
  }
}

// 创建全局实例
const browserPerformanceMonitor = new BrowserPerformanceMonitor();

// 导出
export default browserPerformanceMonitor;
export { BrowserPerformanceMonitor };
