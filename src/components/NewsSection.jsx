import React from 'react';
import { HiSpeakerphone, HiSparkles, HiCalendar, HiArrowRight } from 'react-icons/hi';
import { FaBullhorn, FaNewspaper, FaRocket, FaEye } from 'react-icons/fa';

const NewsSection = () => {
  const newsItems = [
    {
      id: 1,
      type: 'announcement',
      icon: <HiSpeakerphone className="text-2xl" />,
      title: 'YNNX AI DEVELOPER 1.0 发布',
      description: '全新基于AI的开发助手平台，支持多种编程语言，支持多种开发工具。为开发者提供更高效的开发体验。',
      date: '2025-06-18',
      highlight: true,
      link: '#docs'
    },
    {
      id: 2,
      type: 'news',
      icon: <FaNewspaper className="text-2xl" />,
      title: 'YNNX AI DEVELOPER 1.0 开启试运行',
      description: '欢迎各位技术大咖一起加入我们，共同打造一个更加高效的开发环境。近期将陆续发布包括专属IDE, DeepWiki等更多功能，敬请期待。由于算力限制，目前暂提供qwen3-235b-a22b模型，生产环境则提供deepseek-v3-0324以及deepseek-r1, 后续视使用情况进行调整。',
      date: '2025-06-18',
      link: '#docs'
    },
    {
      id: 3,
      type: 'preview',
      icon: <FaEye className="text-2xl" />,
      title: '重磅预告：三大核心应用即将发布',
      description: '近期即将推出 Web IDE 在线开发环境、Web Chat 智能对话系统及 DeepWiki 知识库应用，为开发者提供完整的云端开发生态系统。敬请期待！',
      date: '2025-07-08',
      highlight: false,
      link: '#home'
    }
    // {
    //   id: 2,
    //   type: 'feature',
    //   icon: <HiSparkles className="text-2xl" />,
    //   title: '新功能：智能代码重构',
    //   description: '一键优化代码结构，提升代码质量和可维护性',
    //   date: '2024-01-18',
    //   link: '#ai-assistant'
    // },
    // {
    //   id: 3,
    //   type: 'update',
    //   icon: <FaRocket className="text-2xl" />,
    //   title: 'VS Code 插件更新 v3.2.1',
    //   description: '修复已知问题，优化性能，新增Python支持',
    //   date: '2024-01-15',
    //   link: '#downloads'
    // },
  ];

  const getTypeColor = (type) => {
    switch (type) {
      case 'announcement':
        return 'from-red-500 to-orange-500';
      case 'feature':
        return 'from-purple-500 to-pink-500';
      case 'update':
        return 'from-blue-500 to-cyan-500';
      case 'news':
        return 'from-green-500 to-teal-500';
      case 'preview':
        return 'from-indigo-500 to-purple-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getTypeBadge = (type) => {
    switch (type) {
      case 'announcement':
        return '重要公告';
      case 'feature':
        return '新功能';
      case 'update':
        return '更新';
      case 'news':
        return '新闻';
      case 'preview':
        return '预告';
      default:
        return '动态';
    }
  };

  return (
    <section id="news" className="py-20 bg-gray-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-black via-gray-900 to-black"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-cyan-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题 */}
        <div
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-3 mb-4">
            <FaBullhorn className="text-3xl text-cyan-400" />
            <h2 className="text-5xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
              最新动态
            </h2>
          </div>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            了解 YNNX AI 平台的最新消息、功能更新和重要公告
          </p>
        </div>

        {/* 动态网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {newsItems.map((item) => (
            <div
              key={item.id}
              className={`relative group ${
                item.highlight ? 'md:col-span-2' : ''
              }`}
            >
              <div className={`bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6 h-full hover:border-cyan-500/50 transition-all duration-300 ${
                item.highlight ? 'bg-gradient-to-r from-gray-800/50 to-gray-800/30' : ''
              }`}>
                {/* 高亮标记 */}
                {item.highlight ? (
                  <div className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                    HOT
                  </div>
                ) : null}

                <div className="flex items-start gap-4">
                  {/* 图标 */}
                  <div className={`p-3 bg-gradient-to-r ${getTypeColor(item.type)} rounded-lg text-white flex-shrink-0`}>
                    {item.icon}
                  </div>

                  {/* 内容 */}
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span className={`text-xs font-semibold px-2 py-1 rounded-full bg-gradient-to-r ${getTypeColor(item.type)} text-white`}>
                        {getTypeBadge(item.type)}
                      </span>
                      <span className="text-sm text-gray-400 flex items-center gap-1">
                        <HiCalendar />
                        {item.date}
                      </span>
                    </div>

                    <h3 className={`font-semibold text-white mb-2 ${
                      item.highlight ? 'text-2xl' : 'text-lg'
                    }`}>
                      {item.title}
                    </h3>

                    <p className={`text-gray-400 ${
                      item.highlight ? 'text-base' : 'text-sm'
                    }`}>
                      {item.description}
                    </p>

                    {/* 查看更多链接 */}
                    <a
                      href={item.link}
                      onClick={(e) => {
                        e.preventDefault();
                        const element = document.querySelector(item.link);
                        if (element) {
                          element.scrollIntoView({ behavior: 'smooth' });
                        }
                      }}
                      className="inline-flex items-center gap-2 mt-4 text-cyan-400 hover:text-cyan-300 transition-colors group cursor-pointer"
                    >
                      <span className="text-sm font-medium">了解更多</span>
                      <HiArrowRight className="transition-transform group-hover:translate-x-1" />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 查看所有动态按钮 */}
        <div
          className="text-center mt-12"
        >
          <button
            onClick={() => alert('更多动态功能正在开发中...')}
            className="inline-flex items-center gap-2 px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
          >
            查看所有动态
            <HiArrowRight />
          </button>
        </div>
      </div>
    </section>
  );
};

export default NewsSection; 