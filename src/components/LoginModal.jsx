import React, { useState } from 'react';
import { FaUser, FaLock, FaTimes, FaNetworkWired } from 'react-icons/fa';
import { HiFingerPrint } from 'react-icons/hi';
import ldapService from '../services/ldapService';

const LoginModal = ({ isOpen, onClose, onLogin }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [selectedEnvironment, setSelectedEnvironment] = useState(''); // 默认为空，等待从后端获取环境列表后设置
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [environments, setEnvironments] = useState([]);

  // 获取LDAP环境列表
  React.useEffect(() => {
    if (isOpen) {
      console.log('🔍 LoginModal - 开始获取LDAP环境列表');
      const fetchData = async () => {
        try {
          console.log('🌐 正在调用 ldapService.getEnvironments()...');
          // 获取可用环境列表
          const envResult = await ldapService.getEnvironments();
          console.log('📋 LDAP环境API响应:', envResult);
          
          if (envResult.success) {
            console.log('✅ 成功获取环境列表:', envResult.environments);
            setEnvironments(envResult.environments);
            // 如果有可用环境，设置默认环境为第一个环境
            if (envResult.environments.length > 0) {
              const envIds = envResult.environments.map(env => env.id);
              if (!selectedEnvironment || !envIds.includes(selectedEnvironment)) {
                console.log('🎯 设置默认环境:', envResult.environments[0]);
                setSelectedEnvironment(envResult.environments[0].id);
              }
            } else {
              console.warn('⚠️ 环境列表为空');
            }
          } else {
            console.error('❌ 获取环境失败:', envResult);
            setEnvironments([]);
          }
        } catch (error) {
          console.error('💥 获取LDAP环境列表失败:', error);
          setEnvironments([]);
        }
      };
      fetchData();
    }
  }, [isOpen]); // 移除selectedEnvironment依赖，避免循环调用

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      if (!username || !password) {
        throw new Error('请输入用户名和密码');
      }

      // 用户名过滤：检查是否以 com- 开头
      if (username.toLowerCase().startsWith('com-')) {
        throw new Error('暂无权限，目前仅对内部员工开放');
      }

      const result = await ldapService.authenticate(username, password, selectedEnvironment);

      if (result.success) {
        onLogin(result.user);
        onClose();
        setUsername('');
        setPassword('');
        setError('');
      } else {
        // 根据错误类型显示不同的提示信息
        let errorMessage = result.error || '认证失败';

        // 检查是否为网络错误
        if (result.errorType === 'network' ||
            (result.error && (
              result.error.includes('连接失败') ||
              result.error.includes('服务器连接') ||
              result.error.includes('网络错误')
            ))) {
          errorMessage = '🔌 ' + (result.error || 'LDAP服务器连接失败，请稍后重试');
        } else if (result.errorType === 'authentication' ||
                   (result.error && result.error.includes('用户名或密码错误'))) {
          errorMessage = '🔐 ' + (result.error || '用户名或密码错误，请检查后重试');
        } else {
          errorMessage = '⚠️ ' + errorMessage;
        }

        setError(errorMessage);
      }
    } catch (error) {
      console.error('LDAP认证失败:', error);

      // 处理网络请求异常
      let errorMessage = '⚠️ 认证服务异常，请稍后重试';
      if (error.message) {
        if (error.message.includes('网络错误') ||
            error.message.includes('fetch') ||
            error.message.includes('Failed to fetch')) {
          errorMessage = '🔌 网络连接异常，请检查网络后重试';
        } else {
          errorMessage = '⚠️ ' + error.message;
        }
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取当前选择环境的说明文本
  const getEnvironmentDescription = (envId) => {
    // 从环境列表中查找对应的环境名称
    const selectedEnv = environments.find(env => env.id === envId);
    return selectedEnv ? selectedEnv.name : '请选择LDAP环境';
  };

  return (
    <>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            onClick={onClose}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50"
          />

          {/* 模态框容器 - 使用 flex 布局居中 */}
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-y-auto">
            <div
              className="relative w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="bg-gray-900 rounded-2xl p-8 shadow-2xl border border-gray-800">
                {/* 关闭按钮 */}
                <button
                  onClick={onClose}
                  className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
                >
                  <FaTimes size={20} />
                </button>

                {/* 标题 */}
                <div className="text-center mb-8">
                  <div
                    className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full mb-4"
                  >
                    <HiFingerPrint className="text-4xl text-black" />
                  </div>
                  <h2 className="text-2xl font-bold text-white mb-2">开发云桌面账号身份认证</h2>
                  <p className="text-gray-400 text-sm">{getEnvironmentDescription(selectedEnvironment)}</p>
                </div>

                {/* 登录表单 */}
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* LDAP环境选择 */}
                  {environments.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        请选择云桌面LDAP环境
                      </label>
                      <div className="relative">
                        <FaNetworkWired className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <select
                          value={selectedEnvironment}
                          onChange={(e) => setSelectedEnvironment(e.target.value)}
                          className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-cyan-500 focus:outline-none transition-colors appearance-none"
                        >
                          {environments.map((env) => (
                            <option key={env.id} value={env.id} className="bg-gray-800">
                              {env.name}
                            </option>
                          ))}
                        </select>
                        {/* 自定义下拉箭头 */}
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                          <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {getEnvironmentDescription(selectedEnvironment)}
                      </p>
                    </div>
                  )}

                  {/* 当没有环境时显示提示 */}
                  {environments.length === 0 && (
                    <div className="bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-3 text-yellow-400 text-sm">
                      <div className="flex items-center gap-2">
                        <FaNetworkWired className="text-yellow-400" />
                        <span>正在获取LDAP环境列表...</span>
                      </div>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      用户名
                    </label>
                    <div className="relative">
                      <FaUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <input
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-cyan-500 focus:outline-none transition-colors"
                        placeholder="请输入您的云桌面用户名"
                        autoComplete="username"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      密码
                    </label>
                    <div className="relative">
                      <FaLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <input
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-cyan-500 focus:outline-none transition-colors"
                        placeholder="请输入密码"
                        autoComplete="current-password"
                        required
                      />
                    </div>
                  </div>

                  {/* 错误提示 */}
                  {error && (
                    <div
                      className="bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-400 text-sm"
                    >
                      {error}
                    </div>
                  )}

                  {/* 登录按钮 */}
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full py-3 bg-gradient-to-r from-cyan-400 to-blue-500 text-black font-semibold rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                  >
                    {isLoading ? (
                      <span className="inline-flex items-center gap-2">
                        <span
                          className="inline-block w-4 h-4 border-2 border-black border-t-transparent rounded-full"
                        />
                        认证中...
                      </span>
                    ) : (
                      '立即登录'
                    )}
                  </button>
                </form>

                {/* 安全提示信息 */}
                <div className="mt-6 border-t border-gray-700 pt-4">
                  <div className="text-xs text-gray-500 space-y-1">
                    <div><i className="fas fa-lock text-cyan-400 mr-2"></i>使用您的开发云桌面账号进行认证</div>
                                          <div><i className="fas fa-shield-alt text-yellow-400 mr-2"></i>目前仅向内部员工开放，请勿泄漏相关信息给外部人员</div>
                  </div>
                </div>

                {/* 帮助信息 */}
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default LoginModal; 