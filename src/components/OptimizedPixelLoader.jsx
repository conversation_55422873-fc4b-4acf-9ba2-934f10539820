import React, { useEffect, useState } from 'react';

// 全局状态，防止重复加载
let globalHasStarted = false;
let globalLoadingComplete = false;

const OptimizedPixelLoader = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [showPixels, setShowPixels] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('系统初始化中...');
  const [optimizationLevel, setOptimizationLevel] = useState('medium');

  useEffect(() => {
    // 检测性能优化设置
    const checkPerformanceFlags = () => {
      const flags = window.PERFORMANCE_FLAGS || {};
      
      if (flags['disable-fancy-animations']) {
        setOptimizationLevel('minimal');
      } else if (flags['reduce-animations']) {
        setOptimizationLevel('reduced');
      } else if (flags['enhanced-animations']) {
        setOptimizationLevel('enhanced');
      } else {
        setOptimizationLevel('normal');
      }
    };

    checkPerformanceFlags();

    // 防止重复执行 - 使用全局状态
    if (globalHasStarted) {
      console.log('[OptimizedPixelLoader] 已经启动过，跳过重复执行');
      if (globalLoadingComplete) {
        console.log('[OptimizedPixelLoader] 加载已完成，立即触发完成回调');
        setIsLoading(false);
        onComplete?.();
      }
      return;
    }

    console.log('[OptimizedPixelLoader] 开始启动流程，优化级别:', optimizationLevel);
    globalHasStarted = true;

    // 根据性能级别调整显示延迟
    const pixelDelay = getPixelDelay(optimizationLevel);
    setTimeout(() => setShowPixels(true), pixelDelay);

    // 根据性能级别调整超时时间
    const timeoutDuration = getTimeoutDuration(optimizationLevel);
    const safetyTimeout = setTimeout(() => {
      console.warn('[OptimizedPixelLoader] 安全超时触发，强制完成加载');
      globalLoadingComplete = true;
      setProgress(100);
      setLoadingMessage('启动完成');
      setIsLoading(false);
      onComplete?.();
    }, timeoutDuration);

    // 启动加载流程
    startOptimizedLoad(optimizationLevel, setProgress, setLoadingMessage, () => {
      globalLoadingComplete = true;
      setIsLoading(false);
      onComplete?.();
      clearTimeout(safetyTimeout);
    });

    return () => {
      clearTimeout(safetyTimeout);
    };
  }, [onComplete, optimizationLevel]);

  // 根据性能级别获取像素显示延迟
  const getPixelDelay = (level) => {
    switch (level) {
      case 'minimal': return 0; // 立即显示
      case 'reduced': return 50;
      case 'normal': return 100;
      case 'enhanced': return 200;
      default: return 100;
    }
  };

  // 根据性能级别获取超时时间
  const getTimeoutDuration = (level) => {
    switch (level) {
      case 'minimal': return 2000; // 2秒快速超时
      case 'reduced': return 3000;
      case 'normal': return 5000;
      case 'enhanced': return 8000;
      default: return 5000;
    }
  };

  // 优化的加载流程
  const startOptimizedLoad = async (level, setProgress, setLoadingMessage, onComplete) => {
    try {
      console.log('[OptimizedPixelLoader] 开始优化加载流程，级别:', level);

      if (level === 'minimal') {
        // 最小化模式：快速完成
        setProgress(50);
        setLoadingMessage('快速启动中...');
        
        await new Promise(resolve => setTimeout(resolve, 200));
        setProgress(100);
        setLoadingMessage('启动完成');
        
        setTimeout(onComplete, 300);
        return;
      }

      if (level === 'reduced') {
        // 减少模式：简化流程
        setProgress(30);
        setLoadingMessage('系统初始化...');
        
        await new Promise(resolve => setTimeout(resolve, 200));
        setProgress(70);
        setLoadingMessage('加载组件...');
        
        await new Promise(resolve => setTimeout(resolve, 200));
        setProgress(100);
        setLoadingMessage('启动完成');
        
        setTimeout(onComplete, 300);
        return;
      }

      // 正常和增强模式：完整流程
      const steps = getLoadingSteps(level);
      
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        setProgress(step.progress);
        setLoadingMessage(step.message);
        
        await new Promise(resolve => setTimeout(resolve, step.duration));
      }
      
      setTimeout(onComplete, level === 'enhanced' ? 800 : 500);
      
    } catch (error) {
      console.error('[OptimizedPixelLoader] 启动失败:', error);
      // 即使失败也要继续
      setProgress(100);
      setLoadingMessage('启动完成');
      setTimeout(onComplete, 500);
    }
  };

  // 获取加载步骤
  const getLoadingSteps = (level) => {
    if (level === 'enhanced') {
      return [
        { progress: 20, message: '系统初始化中...', duration: 400 },
        { progress: 40, message: '加载核心模块...', duration: 400 },
        { progress: 60, message: '加载文档库...', duration: 400 },
        { progress: 80, message: '启动AI引擎...', duration: 400 },
        { progress: 100, message: '启动完成', duration: 300 }
      ];
    }
    
    // 正常模式
    return [
      { progress: 20, message: '系统初始化中...', duration: 300 },
      { progress: 50, message: '加载文档库...', duration: 300 },
      { progress: 80, message: '启动AI引擎...', duration: 300 },
      { progress: 100, message: '启动完成', duration: 200 }
    ];
  };

  // 渲染简化版加载器（用于低性能设备）
  const renderSimpleLoader = () => (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black">
      <div className="text-center">
        <div className="text-2xl font-bold text-cyan-400 mb-4">YNNX AI</div>
        <div className="w-64 bg-gray-800 rounded-full h-2">
          <div
            className="h-full bg-cyan-400 rounded-full transition-all duration-200"
            style={{ width: `${progress}%` }}
          />
        </div>
        <div className="text-sm text-gray-300 mt-2">{loadingMessage}</div>
        <div className="text-xs text-gray-500 mt-1">{progress}%</div>
      </div>
    </div>
  );

  // 渲染完整版加载器
  const renderFullLoader = () => {
    // 根据优化级别调整像素网格大小
    const getPixelGrid = () => {
      if (optimizationLevel === 'reduced') {
        // 减少的像素网格
        return [
          [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
          [0,1,0,0,1,0,1,0,0,1,0,1,0,1,0],
          [0,0,1,1,0,0,1,1,0,1,0,0,1,0,0],
          [0,0,1,0,0,0,1,0,1,1,0,0,1,0,0],
          [0,1,0,0,1,0,1,0,0,1,0,1,0,1,0],
          [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
        ];
      }
      
      // 完整像素网格
      return [
        [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
        [0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0],
        [0,0,0,1,0,1,0,0,0,1,1,0,0,1,0,0,1,1,0,0,1,0,0,0,1,0,1,0,0,0],
        [0,0,0,0,1,0,0,0,0,1,0,1,0,1,0,0,1,0,1,0,1,0,0,0,0,1,0,0,0,0],
        [0,0,0,0,1,0,0,0,0,1,0,0,1,1,0,0,1,0,0,1,1,0,0,0,1,0,1,0,0,0],
        [0,0,0,0,1,0,0,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0],
        [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
      ];
    };

    const textPixels = getPixelGrid();

    return (
      <div className={`fixed inset-0 z-50 flex items-center justify-center bg-black transition-opacity duration-500 ${isLoading ? 'opacity-100' : 'opacity-0'}`}>
        <div className="relative w-full h-full flex flex-col items-center justify-center">
          {/* 背景效果 - 在低性能模式下简化 */}
          {optimizationLevel !== 'reduced' && optimizationLevel !== 'minimal' && (
            <div className="absolute inset-0 bg-gradient-radial from-cyan-500/10 to-transparent opacity-50"></div>
          )}

          {/* 像素网格 */}
          <div className="relative z-10 mb-8">
            <div 
              className="grid gap-1 p-4"
              style={{
                gridTemplateColumns: `repeat(${textPixels[0].length}, 1fr)`,
                gridTemplateRows: `repeat(${textPixels.length}, 1fr)`
              }}
            >
              {textPixels.map((row, rowIndex) => 
                row.map((pixel, colIndex) => {
                  const delay = optimizationLevel === 'enhanced' 
                    ? (colIndex * 0.03 + rowIndex * 0.01)
                    : (colIndex * 0.01 + rowIndex * 0.005); // 减少延迟
                  
                  const colorIndex = (colIndex + rowIndex) % 6;
                  const gradients = [
                    'bg-gradient-to-br from-red-400 to-pink-500',
                    'bg-gradient-to-br from-orange-400 to-red-500', 
                    'bg-gradient-to-br from-yellow-400 to-orange-500',
                    'bg-gradient-to-br from-green-400 to-teal-500',
                    'bg-gradient-to-br from-cyan-400 to-blue-500',
                    'bg-gradient-to-br from-blue-400 to-purple-500'
                  ];
                  
                  // 在低性能模式下简化阴影效果
                  const shadows = optimizationLevel === 'reduced' ? [] : [
                    '0 0 10px rgba(248, 113, 113, 0.8)',
                    '0 0 10px rgba(251, 146, 60, 0.8)',
                    '0 0 10px rgba(250, 204, 21, 0.8)', 
                    '0 0 10px rgba(52, 211, 153, 0.8)',
                    '0 0 10px rgba(6, 182, 212, 0.8)',
                    '0 0 10px rgba(139, 92, 246, 0.8)'
                  ];
                  
                  return (
                    <div
                      key={`${rowIndex}-${colIndex}`}
                      className={`w-4 h-4 rounded-sm transition-all ${
                        optimizationLevel === 'enhanced' ? 'duration-300' : 'duration-150'
                      } ${
                        pixel === 1 && showPixels
                          ? `${gradients[colorIndex]} shadow-lg scale-100 opacity-100` 
                          : 'bg-gray-800/20 scale-75 opacity-30'
                      }`}
                      style={{
                        transitionDelay: showPixels ? `${delay}s` : '0s',
                        boxShadow: pixel === 1 && showPixels && shadows.length > 0 
                          ? shadows[colorIndex] : 'none'
                      }}
                    />
                  );
                })
              )}
            </div>
          </div>

          {/* 进度条和加载信息 */}
          <div className="w-80 max-w-sm mx-auto">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-mono bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent animate-pulse">
                {loadingMessage}
              </span>
              <span className="text-sm font-mono bg-gradient-to-r from-green-400 to-cyan-300 bg-clip-text text-transparent font-semibold">
                {progress.toFixed(0)}%
              </span>
            </div>
            <div className="w-full bg-gray-800 rounded-full h-2 overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-green-400 via-cyan-400 via-blue-500 to-purple-500 rounded-full transition-all duration-200 shadow-lg shadow-cyan-500/50"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {/* 提示文字 */}
          <div className="mt-6 text-center">
            <p className="text-sm bg-gradient-to-r from-gray-300 via-cyan-200 to-blue-300 bg-clip-text text-transparent">
              YNNX AI 智能平台正在启动...
            </p>
            {optimizationLevel === 'minimal' && (
              <p className="text-xs text-gray-500 mt-1">性能优化模式</p>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (!isLoading) return null;

  // 根据优化级别选择渲染方式
  if (optimizationLevel === 'minimal') {
    return renderSimpleLoader();
  }

  return renderFullLoader();
};

export default OptimizedPixelLoader;