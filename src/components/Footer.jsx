import React from 'react';

const Footer = () => {
  const footerLinks = [
    {
      title: '产品',
      links: [
        { name: 'AI 代码助手', href: '#chat' },
        { name: 'API 密钥管理', href: '#api-key' },
        { name: '工具下载', href: '#downloads' },
        { name: '使用文档', href: '#docs' }
      ]
    },
    {
      title: '功能导航',
      links: [
        { name: '平台数据', href: '#features' },
        { name: 'AI 智能助手', href: '#chat' },
        { name: '开发工具', href: '#downloads' },
        { name: '最新动态', href: '#news' }
      ]
    },
    {
      title: '帮助支持',
      links: [
        { name: '快速开始', href: '#docs' },
        { name: '文档中心', href: '#docs' },
        { name: '平台首页', href: '#home' },
        { name: '联系我们', href: '#docs' }
      ]
    }
  ];

  return (
    <footer className="bg-gray-900 border-t border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* 主要内容 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
          {/* Logo 和介绍 */}
          <div className="lg:col-span-1">
            <div
              className="space-y-6"
            >
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-cyan-400 via-blue-400 to-purple-500 blur-lg opacity-30"></div>
                  <div className="relative bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-cyan-400 via-blue-400 to-purple-500 text-black font-black text-xl px-4 py-2 rounded-lg shadow-lg">
                    YNNX
                  </div>
                </div>
                <span className="text-white font-black text-xl bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                  AI Platform
                </span>
              </div>
              
              <div className="space-y-3">
                <h4 className="text-white font-semibold text-sm">云南农信智能开发平台</h4>
                <p className="text-gray-400 text-sm leading-relaxed">
                  为开发者提供强大的 AI 编程助手和工具支持，基于AI与开源技术自主研发，AI代码量超过99%
                </p>
              </div>
            </div>
          </div>

          {/* 链接列表 */}
          {footerLinks.map((section) => (
            <div
              key={section.title}
              className="space-y-4"
            >
              <h3 className="text-white font-semibold text-lg">{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-gray-400 hover:text-cyan-400 text-sm transition-colors duration-200 hover:underline"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* 底部信息 */}
        <div className="border-t border-gray-800 pt-8 space-y-6">
          {/* 版权和链接 */}
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
            <div
              className="space-y-2"
            >
              <p className="text-gray-400 text-sm">
                © 2025 云南农信科技结算中心. All rights reserved.
              </p>
            </div>
            
            <div
              className="flex flex-wrap gap-6 text-sm"
            >
              <a href="#docs" className="text-gray-400 hover:text-cyan-400 transition-colors duration-200">
                使用指南
              </a>
              <a href="#chat" className="text-gray-400 hover:text-cyan-400 transition-colors duration-200">
                AI 助手
              </a>
              <a href="#api-key" className="text-gray-400 hover:text-cyan-400 transition-colors duration-200">
                API 管理
              </a>
              <a href="#downloads" className="text-gray-400 hover:text-cyan-400 transition-colors duration-200">
                工具下载
              </a>
            </div>
          </div>

          {/* 技术支持和欢迎信息 */}
          <div className="text-center pt-6 border-t border-gray-800">
            <p className="text-gray-500 text-sm">
              欢迎各位技术大咖加入我们，共同打造一个更加高效的开发环境 <i className="fas fa-rocket text-cyan-400 ml-1"></i>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 