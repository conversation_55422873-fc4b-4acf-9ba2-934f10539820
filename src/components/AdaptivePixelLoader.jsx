import React, { useEffect, useState } from 'react';

// 全局状态，防止重复加载
let globalHasStarted = false;
let globalLoadingComplete = false;

/**
 * 自适应像素加载器 - 根据设备性能自动调整复杂度
 * 用户无法感知优化过程，系统自动提供最佳体验
 */
const AdaptivePixelLoader = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [showPixels, setShowPixels] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('系统初始化中...');
  const [adaptiveMode, setAdaptiveMode] = useState('detecting');

  useEffect(() => {
    // 防止重复执行
    if (globalHasStarted) {
      if (globalLoadingComplete) {
        setIsLoading(false);
        onComplete?.();
      }
      return;
    }

    console.log('[AdaptivePixelLoader] 启动自适应加载');
    globalHasStarted = true;

    // 自动检测并应用最适合的加载模式
    const initializeAdaptiveLoading = async () => {
      // 等待性能管理器初始化（如果还没有完成）
      const waitForPerformanceManager = () => {
        return new Promise(resolve => {
          const check = () => {
            if (window.autoPerformanceManager?.isInitialized) {
              resolve();
            } else {
              setTimeout(check, 10);
            }
          };
          check();
        });
      };

      await waitForPerformanceManager();

      // 获取当前优化级别并自动适应
      const performanceStatus = window.autoPerformanceManager.getPerformanceStatus();
      const mode = determineLoadingMode(performanceStatus.currentLevel);
      
      setAdaptiveMode(mode);
      console.log(`[AdaptivePixelLoader] 自动选择加载模式: ${mode}`);

      // 根据模式启动相应的加载流程
      await startAdaptiveLoading(mode);
    };

    initializeAdaptiveLoading();

    // 安全超时机制 - 时间根据性能自动调整
    const getTimeoutDuration = (flags = {}) => {
      if (flags['emergency-mode']) return 1000;  // 紧急模式1秒
      if (flags['minimal-ui']) return 2000;      // 最小UI 2秒
      if (flags['reduce-animations']) return 3000; // 减少动画3秒
      return 5000; // 正常模式5秒
    };

    const timeoutDuration = getTimeoutDuration(window.PERFORMANCE_FLAGS);
    const safetyTimeout = setTimeout(() => {
      console.warn('[AdaptivePixelLoader] 安全超时触发');
      completeLoading();
    }, timeoutDuration);

    const completeLoading = () => {
      globalLoadingComplete = true;
      setProgress(100);
      setLoadingMessage('启动完成');
      setIsLoading(false);
      onComplete?.();
      clearTimeout(safetyTimeout);
    };

    // 启动自适应加载
    const startAdaptiveLoading = async (mode) => {
      try {
        switch (mode) {
          case 'instant':
            await instantLoad();
            break;
          case 'minimal':
            await minimalLoad();
            break;
          case 'standard':
            await standardLoad();
            break;
          case 'enhanced':
            await enhancedLoad();
            break;
          default:
            await standardLoad();
        }
        completeLoading();
      } catch (error) {
        console.error('[AdaptivePixelLoader] 加载失败:', error);
        completeLoading();
      }
    };

    // 即时加载模式 - 极低性能设备
    const instantLoad = async () => {
      setProgress(100);
      setLoadingMessage('快速启动');
      // 不显示像素动画，直接完成
    };

    // 最小加载模式 - 低性能设备  
    const minimalLoad = async () => {
      setShowPixels(true);
      setProgress(50);
      setLoadingMessage('加载中...');
      await sleep(100);
      setProgress(100);
      setLoadingMessage('启动完成');
    };

    // 标准加载模式 - 中等性能设备
    const standardLoad = async () => {
      setTimeout(() => setShowPixels(true), 50);
      
      await sleep(200);
      setProgress(30);
      setLoadingMessage('系统初始化...');
      
      await sleep(200);
      setProgress(70);
      setLoadingMessage('加载组件...');
      
      await sleep(200);
      setProgress(100);
      setLoadingMessage('启动完成');
    };

    // 增强加载模式 - 高性能设备
    const enhancedLoad = async () => {
      setTimeout(() => setShowPixels(true), 100);
      
      const steps = [
        { progress: 20, message: '系统初始化中...', delay: 300 },
        { progress: 40, message: '加载核心模块...', delay: 300 },
        { progress: 60, message: '加载文档库...', delay: 300 },
        { progress: 80, message: '启动AI引擎...', delay: 300 },
        { progress: 100, message: '启动完成', delay: 200 }
      ];

      for (const step of steps) {
        setProgress(step.progress);
        setLoadingMessage(step.message);
        await sleep(step.delay);
      }
    };

    return () => {
      clearTimeout(safetyTimeout);
    };
  }, [onComplete]);

  /**
   * 根据性能级别自动确定加载模式
   */
  const determineLoadingMode = (performanceLevel) => {
    const flags = window.PERFORMANCE_FLAGS || {};
    
    // 检查紧急模式标志
    if (flags['emergency-mode']) return 'instant';
    if (flags['minimal-ui']) return 'minimal';
    
    // 根据性能级别自动选择
    switch (performanceLevel) {
      case 'maximum': return 'instant';    // 最大优化 = 即时加载
      case 'aggressive': return 'minimal'; // 积极优化 = 最小加载
      case 'balanced': return 'standard';  // 平衡优化 = 标准加载
      case 'minimal': return 'enhanced';   // 最小优化 = 增强加载
      default: return 'standard';
    }
  };

  /**
   * 获取自适应像素网格 - 所有模式都保持完整的YNNX字母
   */
  const getAdaptivePixelGrid = () => {
    // 完整的YNNX字母网格（所有模式都使用这个）
    const completeYNNXGrid = [
      [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
      [0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0],
      [0,0,0,1,0,1,0,0,0,1,1,0,0,1,0,0,1,1,0,0,1,0,0,0,1,0,1,0,0,0],
      [0,0,0,0,1,0,0,0,0,1,0,1,0,1,0,0,1,0,1,0,1,0,0,0,0,1,0,0,0,0],
      [0,0,0,0,1,0,0,0,0,1,0,0,1,1,0,0,1,0,0,1,1,0,0,0,1,0,1,0,0,0],
      [0,0,0,0,1,0,0,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0],
      [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
    ];

    switch (adaptiveMode) {
      case 'instant':
        // 即时模式也显示完整字母，但无动画效果
        return completeYNNXGrid;
        
      case 'minimal':
        // 最小模式显示完整字母，简化动画
        return completeYNNXGrid;
        
      case 'standard':
        // 标准模式显示完整字母，中等动画
        return completeYNNXGrid;
        
      case 'enhanced':
        // 增强模式显示完整字母，完整动画效果
        return completeYNNXGrid;
        
      default:
        return completeYNNXGrid; // 默认也显示完整字母
    }
  };

  /**
   * 渲染即时模式（显示字母但无复杂动画）
   */
  const renderInstantMode = () => {
    const textPixels = getAdaptivePixelGrid();
    
    return (
      <div className={`fixed inset-0 z-50 flex items-center justify-center bg-black transition-opacity duration-300 ${isLoading ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
        <div className="relative w-full h-full flex flex-col items-center justify-center">
          {/* 简化的像素网格 - 无复杂动画 */}
          <div className="relative z-10 mb-8">
            <div 
              className="grid gap-1 p-4"
              style={{
                gridTemplateColumns: `repeat(${textPixels[0]?.length || 1}, 1fr)`,
                gridTemplateRows: `repeat(${textPixels.length}, 1fr)`
              }}
            >
              {textPixels.map((row, rowIndex) => 
                row.map((pixel, colIndex) => (
                  <div
                    key={`${rowIndex}-${colIndex}`}
                    className={`w-3 h-3 rounded-sm transition-opacity duration-100 ${
                      pixel === 1 && showPixels
                        ? 'bg-cyan-400 opacity-100' 
                        : 'bg-gray-800/20 opacity-30'
                    }`}
                  />
                ))
              )}
            </div>
          </div>

          {/* 简化的进度信息 */}
          <div className="w-80 max-w-sm mx-auto text-center">
            <div className="text-sm text-cyan-400 mb-2">{loadingMessage}</div>
            <div className="text-xs text-gray-400">{progress.toFixed(0)}%</div>
          </div>

          {/* 提示文字 */}
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-300">
              YNNX AI 智能平台正在启动...
            </p>
          </div>
        </div>
      </div>
    );
  };

  /**
   * 渲染标准模式
   */
  const renderStandardMode = () => {
    const textPixels = getAdaptivePixelGrid();
    
    // 即时模式使用特殊渲染
    if (adaptiveMode === 'instant') {
      return renderInstantMode();
    }

    return (
      <div className={`fixed inset-0 z-50 flex items-center justify-center bg-black transition-opacity duration-500 ${isLoading ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
        <div className="relative w-full h-full flex flex-col items-center justify-center">
          
          {/* 背景效果 - 仅在增强模式显示 */}
          {adaptiveMode === 'enhanced' && (
            <div className="absolute inset-0 bg-gradient-radial from-cyan-500/10 to-transparent opacity-50"></div>
          )}

          {/* 像素网格 - 根据模式调整动画复杂度 */}
          {textPixels.length > 0 && (
            <div className="relative z-10 mb-8">
              <div 
                className="grid gap-1 p-4"
                style={{
                  gridTemplateColumns: `repeat(${textPixels[0]?.length || 1}, 1fr)`,
                  gridTemplateRows: `repeat(${textPixels.length}, 1fr)`
                }}
              >
                {textPixels.map((row, rowIndex) => 
                  row.map((pixel, colIndex) => {
                    // 根据性能模式调整动画延迟和复杂度
                    let delay, pixelSize, transitionDuration;
                    
                    switch (adaptiveMode) {
                      case 'minimal':
                        delay = colIndex * 0.005; // 极快延迟
                        pixelSize = 'w-3 h-3'; // 较小像素
                        transitionDuration = 'duration-100'; // 快速过渡
                        break;
                      case 'standard':
                        delay = colIndex * 0.015; // 中等延迟
                        pixelSize = 'w-4 h-4'; // 标准像素
                        transitionDuration = 'duration-200'; // 标准过渡
                        break;
                      case 'enhanced':
                        delay = colIndex * 0.03 + rowIndex * 0.01; // 复杂延迟
                        pixelSize = 'w-4 h-4'; // 标准像素
                        transitionDuration = 'duration-300'; // 慢速过渡
                        break;
                      default:
                        delay = colIndex * 0.01;
                        pixelSize = 'w-4 h-4';
                        transitionDuration = 'duration-200';
                    }
                    
                    const colorIndex = (colIndex + rowIndex) % 6;
                    
                    // 根据性能模式选择颜色复杂度
                    const getPixelStyle = () => {
                      if (adaptiveMode === 'minimal') {
                        // 最小模式：单色
                        return 'bg-cyan-400';
                      } else if (adaptiveMode === 'standard') {
                        // 标准模式：简单渐变
                        const simpleColors = [
                          'bg-cyan-400', 'bg-blue-400', 'bg-purple-400',
                          'bg-green-400', 'bg-yellow-400', 'bg-red-400'
                        ];
                        return simpleColors[colorIndex];
                      } else {
                        // 增强模式：复杂渐变
                        const gradients = [
                          'bg-gradient-to-br from-red-400 to-pink-500',
                          'bg-gradient-to-br from-orange-400 to-red-500', 
                          'bg-gradient-to-br from-yellow-400 to-orange-500',
                          'bg-gradient-to-br from-green-400 to-teal-500',
                          'bg-gradient-to-br from-cyan-400 to-blue-500',
                          'bg-gradient-to-br from-blue-400 to-purple-500'
                        ];
                        return gradients[colorIndex];
                      }
                    };
                    
                    // 阴影效果仅在增强模式显示
                    const shadowEnabled = adaptiveMode === 'enhanced';
                    const shadows = shadowEnabled ? [
                      '0 0 10px rgba(248, 113, 113, 0.8)',
                      '0 0 10px rgba(251, 146, 60, 0.8)',
                      '0 0 10px rgba(250, 204, 21, 0.8)', 
                      '0 0 10px rgba(52, 211, 153, 0.8)',
                      '0 0 10px rgba(6, 182, 212, 0.8)',
                      '0 0 10px rgba(139, 92, 246, 0.8)'
                    ] : [];
                    
                    return (
                      <div
                        key={`${rowIndex}-${colIndex}`}
                        className={`${pixelSize} rounded-sm transition-all ${transitionDuration} ${
                          pixel === 1 && showPixels
                            ? `${getPixelStyle()} ${shadowEnabled ? 'shadow-lg' : ''} scale-100 opacity-100` 
                            : 'bg-gray-800/20 scale-75 opacity-30'
                        }`}
                        style={{
                          transitionDelay: showPixels ? `${delay}s` : '0s',
                          boxShadow: pixel === 1 && showPixels && shadowEnabled 
                            ? shadows[colorIndex] : 'none'
                        }}
                      />
                    );
                  })
                )}
              </div>
            </div>
          )}

          {/* 进度条和信息 - 自适应显示 */}
          <div className="w-80 max-w-sm mx-auto">
            <div className="flex justify-between items-center mb-2">
              <span className={`text-sm font-mono bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent ${
                adaptiveMode === 'enhanced' ? 'animate-pulse' : ''
              }`}>
                {loadingMessage}
              </span>
              <span className="text-sm font-mono bg-gradient-to-r from-green-400 to-cyan-300 bg-clip-text text-transparent font-semibold">
                {progress.toFixed(0)}%
              </span>
            </div>
            <div className="w-full bg-gray-800 rounded-full h-2 overflow-hidden">
              <div
                className={`h-full bg-gradient-to-r from-green-400 via-cyan-400 via-blue-500 to-purple-500 rounded-full transition-all duration-200 ${
                  adaptiveMode === 'enhanced' ? 'shadow-lg shadow-cyan-500/50' : ''
                }`}
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {/* 提示文字 */}
          <div className="mt-6 text-center">
            <p className="text-sm bg-gradient-to-r from-gray-300 via-cyan-200 to-blue-300 bg-clip-text text-transparent">
              YNNX AI 智能平台正在启动...
            </p>
            {/* 仅开发模式显示优化信息 */}
            {process.env.NODE_ENV === 'development' && (
              <p className="text-xs text-gray-500 mt-1">
                自动优化模式: {adaptiveMode}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  };

  // 辅助函数
  const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

  if (!isLoading) return null;

  return renderStandardMode();
};

export default AdaptivePixelLoader;