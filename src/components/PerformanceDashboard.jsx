import React, { useState, useEffect } from 'react';

/**
 * 性能监控仪表板 - 实时显示性能状态
 */
const PerformanceDashboard = ({ show = false }) => {
  const [performanceData, setPerformanceData] = useState({
    fps: 0,
    memory: { used: 0, total: 0 },
    loadTime: 0,
    deviceScore: 0,
    optimizationLevel: 'unknown',
    longTasks: 0,
    networkRequests: 0
  });

  const [isVisible, setIsVisible] = useState(show);

  useEffect(() => {
    if (!isVisible) return;

    let animationFrame;
    let lastTime = performance.now();
    let frameCount = 0;

    // FPS监控
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        frameCount = 0;
        lastTime = currentTime;
        
        setPerformanceData(prev => ({
          ...prev,
          fps
        }));
      }
      
      animationFrame = requestAnimationFrame(measureFPS);
    };

    measureFPS();

    // 内存监控
    const updateMemoryInfo = () => {
      if (performance.memory) {
        setPerformanceData(prev => ({
          ...prev,
          memory: {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
          }
        }));
      }
    };

    // 获取性能优化器的数据
    const updateOptimizerData = () => {
      if (window.performanceOptimizer) {
        const report = window.performanceOptimizer.getPerformanceReport();
        setPerformanceData(prev => ({
          ...prev,
          deviceScore: report.deviceCapabilities?.score || 0,
          optimizationLevel: report.deviceCapabilities?.level || 'unknown'
        }));
      }
    };

    // 长任务监控
    if (window.PerformanceObserver) {
      let longTaskCount = 0;
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            longTaskCount++;
            setPerformanceData(prev => ({
              ...prev,
              longTasks: longTaskCount
            }));
          }
        }
      });
      
      try {
        observer.observe({ entryTypes: ['longtask'] });
      } catch {
        console.warn('长任务监控不支持');
      }
    }

    const interval = setInterval(() => {
      updateMemoryInfo();
      updateOptimizerData();
    }, 1000);

    return () => {
      cancelAnimationFrame(animationFrame);
      clearInterval(interval);
    };
  }, [isVisible]);

  // 获取性能等级颜色
  const getPerformanceColor = (value, thresholds) => {
    if (value >= thresholds.good) return 'text-green-400';
    if (value >= thresholds.warning) return 'text-yellow-400';
    return 'text-red-400';
  };

  // 获取优化建议
  const getOptimizationSuggestions = () => {
    const suggestions = [];
    
    if (performanceData.fps < 30) {
      suggestions.push('帧率较低，建议减少动画效果');
    }
    
    if (performanceData.memory.used > 100) {
      suggestions.push('内存使用过高，建议刷新页面');
    }
    
    if (performanceData.longTasks > 5) {
      suggestions.push('检测到多个长任务，页面可能卡顿');
    }
    
    if (performanceData.deviceScore < 40) {
      suggestions.push('设备性能较低，已启用优化模式');
    }
    
    return suggestions;
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700"
          title="显示性能监控"
        >
          📊
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-80 bg-black/90 text-white p-4 rounded-lg text-xs font-mono z-50 max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-cyan-400">性能监控</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>
      
      {/* 核心性能指标 */}
      <div className="space-y-2">
        <div className="flex justify-between">
          <span>FPS:</span>
          <span className={getPerformanceColor(performanceData.fps, { good: 50, warning: 30 })}>
            {performanceData.fps}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>内存:</span>
          <span className={getPerformanceColor(100 - performanceData.memory.used, { good: 70, warning: 50 })}>
            {performanceData.memory.used}MB / {performanceData.memory.total}MB
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>设备评分:</span>
          <span className={getPerformanceColor(performanceData.deviceScore, { good: 80, warning: 60 })}>
            {performanceData.deviceScore}/100
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>优化级别:</span>
          <span className={
            performanceData.optimizationLevel === 'high' ? 'text-green-400' :
            performanceData.optimizationLevel === 'medium' ? 'text-yellow-400' :
            'text-red-400'
          }>
            {performanceData.optimizationLevel}
          </span>
        </div>
        
        {performanceData.longTasks > 0 && (
          <div className="flex justify-between">
            <span>长任务:</span>
            <span className="text-red-400">{performanceData.longTasks}</span>
          </div>
        )}
      </div>
      
      {/* 优化建议 */}
      {getOptimizationSuggestions().length > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-700">
          <div className="font-bold text-yellow-400 mb-2">优化建议:</div>
          <ul className="space-y-1">
            {getOptimizationSuggestions().map((suggestion, index) => (
              <li key={index} className="text-yellow-300 text-xs">
                • {suggestion}
              </li>
            ))}
          </ul>
        </div>
      )}
      
      {/* 快速操作 */}
      <div className="mt-3 pt-3 border-t border-gray-700 space-y-2">
        <button
          onClick={() => {
            if (window.performanceOptimizer) {
              console.log(window.performanceOptimizer.getPerformanceReport());
            }
          }}
          className="w-full bg-blue-600 hover:bg-blue-500 px-2 py-1 rounded text-xs"
        >
          输出详细报告到控制台
        </button>
        
        <button
          onClick={() => {
            if (window.gc) {
              window.gc();
              console.log('手动触发垃圾回收');
            } else {
              console.log('垃圾回收不可用');
            }
          }}
          className="w-full bg-green-600 hover:bg-green-500 px-2 py-1 rounded text-xs"
        >
          手动垃圾回收
        </button>
        
        <button
          onClick={() => {
            const flags = window.PERFORMANCE_FLAGS || {};
            const currentLevel = flags['disable-fancy-animations'] ? 'minimal' :
                               flags['reduce-animations'] ? 'reduced' : 'normal';
            
            if (currentLevel === 'normal') {
              window.PERFORMANCE_FLAGS = { ...flags, 'reduce-animations': true };
              alert('已启用动画减少模式，刷新页面生效');
            } else if (currentLevel === 'reduced') {
              window.PERFORMANCE_FLAGS = { ...flags, 'disable-fancy-animations': true, 'reduce-animations': false };
              alert('已启用最小化模式，刷新页面生效');
            } else {
              window.PERFORMANCE_FLAGS = {};
              alert('已恢复正常模式，刷新页面生效');
            }
          }}
          className="w-full bg-purple-600 hover:bg-purple-500 px-2 py-1 rounded text-xs"
        >
          切换优化模式
        </button>
      </div>
      
      {/* 实时图表（简单的文本图表） */}
      <div className="mt-3 pt-3 border-t border-gray-700">
        <div className="font-bold text-cyan-400 mb-2">FPS趋势:</div>
        <div className="flex items-end space-x-1 h-8">
          {Array.from({ length: 20 }, (_, i) => (
            <div
              key={i}
              className="w-1 bg-green-400"
              style={{
                height: `${Math.random() * 100}%`,
                opacity: 1 - i * 0.05
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard;