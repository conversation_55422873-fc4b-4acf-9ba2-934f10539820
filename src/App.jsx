import React, { useState, useEffect, Suspense, lazy } from 'react';
import AdaptivePixelLoader from './components/AdaptivePixelLoader';
import Navbar from './components/Navbar';
import HeroSection from './components/HeroSection';
import FeaturesGuideSection from './components/FeaturesGuideSection';
import Footer from './components/Footer';

import ResourcePreloader from './components/ResourcePreloader';
import ErrorBoundary from './components/ErrorBoundary';
import { withIconFallback } from './components/IconFallback';
import authManager from './services/authManager';
// 懒加载工具类，减少初始bundle大小
const loadUtilities = async () => {
  try {
    await Promise.all([
      import('./utils/emojiCompatibility'),
      import('./utils/domErrorHandler'),
      import('./utils/errorCheck')
    ]);
  } catch (error) {
    console.warn('Failed to load utilities:', error);
  }
};



// 懒加载非首屏组件 - 按需加载策略
const APIKeySection = lazy(() => import(/* webpackChunkName: "api-key" */ './components/APIKeySection'));
const WebIDESection = lazy(() => import(/* webpackChunkName: "webide" */ './components/WebIDESection'));
const ChatSection = lazy(() => import(/* webpackChunkName: "chat" */ './components/ChatSection'));
const DownloadsSection = lazy(() => import(/* webpackChunkName: "downloads" */ './components/DownloadsSection'));
const DocumentationSection = lazy(() => import(/* webpackChunkName: "documentation" */ './components/DocumentationSection'));
const NewsSection = lazy(() => import(/* webpackChunkName: "news" */ './components/NewsSection'));
const LoginModal = lazy(() => import(/* webpackChunkName: "login" */ './components/LoginModal'));

// 加载占位组件
const LoadingPlaceholder = ({ height = "500px" }) => (
  <div className={`flex items-center justify-center bg-gray-900/50 rounded-lg animate-pulse`} style={{ height }}>
    <div className="text-cyan-400 text-lg">Loading...</div>
  </div>
);

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [authMessage, setAuthMessage] = useState('');
  
  // 按需加载状态
  const [webIDELoaded, setWebIDELoaded] = useState(false);
  const [chatLoaded, setChatLoaded] = useState(false);

  useEffect(() => {
    // 异步加载工具类
    loadUtilities();

    // 使用 authManager 检查用户登录状态
    const currentUser = authManager.getCurrentUser();
    if (currentUser) {
      setUser(currentUser);
    }

    // 监听认证状态变化事件
    const handleAuthStateChange = (event) => {
      const { type, reason, remaining } = event.detail;
      
      switch (type) {
        case 'expired':
          setUser(null);
          setAuthMessage('登录已过期，请重新登录');
          setTimeout(() => setAuthMessage(''), 5000);
          break;
          
        case 'logout':
          setUser(null);
          if (reason === 'timeout') {
            setAuthMessage('登录超时，已自动注销');
            setTimeout(() => setAuthMessage(''), 5000);
          }
          break;
          
        case 'expiring':
          setAuthMessage(`登录将在${remaining.formattedTime}后过期`);
          setTimeout(() => setAuthMessage(''), 10000);
          break;
          
        default:
          break;
      }
    };

    // 处理显示登录模态框事件
    const handleShowLoginModal = () => {
      setShowLoginModal(true);
    };

    window.addEventListener('authStateChange', handleAuthStateChange);
    window.addEventListener('showLoginModal', handleShowLoginModal);
    
    return () => {
      window.removeEventListener('authStateChange', handleAuthStateChange);
      window.removeEventListener('showLoginModal', handleShowLoginModal);
    };
  }, []);

  const handleLoaderComplete = () => {
    // 延迟一点时间确保过渡动画完成，防止背景闪烁
    setTimeout(() => {
      setIsLoading(false);
    }, 150);
  };

  const handleLogin = (userData) => {
    // 使用 authManager 保存登录状态
    authManager.saveUserLogin(userData);
    setUser(userData);
    setShowLoginModal(false);
    
    // 显示登录成功消息
    const status = authManager.getLoginStatus();
    if (status.remaining && !status.remaining.expired) {
      setAuthMessage(`登录成功！登录有效期: ${status.remaining.formattedTime}`);
      setTimeout(() => setAuthMessage(''), 5000);
    }
  };

  const handleLogout = () => {
    // 使用 authManager 注销
    authManager.logout();
    setUser(null);
  };

  return (
    <ErrorBoundary>
      {/* 确保始终有黑色背景容器 */}
      <div className="min-h-screen bg-black text-white" style={{ backgroundColor: '#000000' }}>
        {/* 资源预加载 */}
        <ResourcePreloader />
        
        {/* 像素加载动画 - 只在加载时显示 */}
        {isLoading && <AdaptivePixelLoader onComplete={handleLoaderComplete} />}

        {/* 主内容 */}
        {!isLoading && (
              <div className="min-h-screen bg-black text-white transition-opacity duration-300 opacity-100">
              {/* 性能模式切换提示 */}
              {authMessage && (
                <div className="fixed top-4 right-4 z-40 bg-cyan-900/90 text-cyan-100 px-4 py-2 rounded-lg shadow-lg">
                  {authMessage}
                </div>
              )}
              
              {/* 导航栏 */}
              <Navbar 
                user={user} 
                onLogin={() => setShowLoginModal(true)} 
                onLogout={handleLogout}
              />

              {/* 主要内容区域 */}
              <main>
                {/* 英雄区域 */}
                <div id="home">
                  <HeroSection />
                </div>

                {/* 平台数据 */}
                <div id="features">
                  <FeaturesGuideSection />
                </div>


                {/* API密钥管理 */}
                <Suspense fallback={<LoadingPlaceholder />}>
                  <APIKeySection 
                    user={user} 
                    onLogin={() => setShowLoginModal(true)}
                  />
                </Suspense>

                {/* Web IDE */}
                <div id="webide">
                  <section className="py-20 px-4 sm:px-6 lg:px-8">
                    <div className="max-w-7xl mx-auto">
                      <div className="text-center mb-16">
                        <h2 className="text-4xl font-black text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 mb-6">
                          Web IDE
                        </h2>
                        <p className="text-xl text-gray-400 max-w-2xl mx-auto mb-4">
                          在线开发环境，基于 VSCode 提供完整的AI代码编辑和开发体验
                        </p>
                        <p className="text-lg text-gray-500 max-w-2xl mx-auto mb-8">
                          若需获得完整体验，非WIN7用户请于下方安装VSCode及AI插件
                        </p>
                        
                        {!webIDELoaded ? (
                          <div className="bg-gray-900/50 rounded-2xl p-12 border border-gray-800 backdrop-blur-sm">
                            <div className="text-center">
                              <div className="inline-flex items-center justify-center w-20 h-20 mb-6 bg-gradient-to-br from-cyan-500/20 to-blue-600/20 rounded-2xl">
                                <svg className="w-10 h-10 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                                </svg>
                              </div>
                              <h3 className="text-2xl font-bold text-white mb-3">准备启动 Web IDE</h3>
                              <p className="text-gray-400 mb-8 max-w-md mx-auto">
                                点击下方按钮加载在线开发环境，提供完整的 VSCode 体验
                              </p>
                              {user ? (
                                <button
                                  onClick={() => setWebIDELoaded(true)}
                                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold rounded-xl hover:from-cyan-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-cyan-500/25"
                                >
                                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-7V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 0V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3" />
                                  </svg>
                                  启动 Web IDE
                                </button>
                              ) : (
                                <button
                                  onClick={() => setShowLoginModal(true)}
                                  className="inline-flex items-center px-8 py-4 bg-gray-700 text-gray-300 font-semibold rounded-xl hover:bg-gray-600 transform hover:scale-105 transition-all duration-200"
                                >
                                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                  </svg>
                                  登录后使用
                                </button>
                              )}
                            </div>
                          </div>
                        ) : (
                          <Suspense fallback={<LoadingPlaceholder height="600px" />}>
                            <WebIDESection 
                              user={user} 
                              onLogin={() => setShowLoginModal(true)}
                            />
                          </Suspense>
                        )}
                      </div>
                    </div>
                  </section>
                </div>

                {/* 智能对话 */}
                <div id="chat">
                  <section className="py-20 px-4 sm:px-6 lg:px-8">
                    <div className="max-w-7xl mx-auto">
                      <div className="text-center mb-16">
                        <h2 className="text-4xl font-black text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 mb-6">
                          智能对话
                        </h2>
                        <p className="text-xl text-gray-400 max-w-2xl mx-auto mb-4">
                          基于 LobeChat 提供先进的AI对话体验，支持多模态交互和智能助手功能
                        </p>
                        <p className="text-lg text-gray-500 max-w-2xl mx-auto mb-8">
                          安全的用户会话隔离，保护您的对话隐私
                        </p>
                        
                        {!chatLoaded ? (
                          <div className="bg-gray-900/50 rounded-2xl p-12 border border-gray-800 backdrop-blur-sm">
                            <div className="text-center">
                              <div className="inline-flex items-center justify-center w-20 h-20 mb-6 bg-gradient-to-br from-green-500/20 to-emerald-600/20 rounded-2xl">
                                <svg className="w-10 h-10 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                              </div>
                              <h3 className="text-2xl font-bold text-white mb-3">准备启动智能对话</h3>
                              <p className="text-gray-400 mb-8 max-w-md mx-auto">
                                点击下方按钮加载 LobeChat 智能对话界面，享受先进的 AI 对话体验
                              </p>
                              {user ? (
                                <button
                                  onClick={() => setChatLoaded(true)}
                                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-emerald-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-green-500/25"
                                >
                                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 4v-4z" />
                                  </svg>
                                  启动智能对话
                                </button>
                              ) : (
                                <button
                                  onClick={() => setShowLoginModal(true)}
                                  className="inline-flex items-center px-8 py-4 bg-gray-700 text-gray-300 font-semibold rounded-xl hover:bg-gray-600 transform hover:scale-105 transition-all duration-200"
                                >
                                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                  </svg>
                                  登录后使用
                                </button>
                              )}
                            </div>
                          </div>
                        ) : (
                          <Suspense fallback={<LoadingPlaceholder height="600px" />}>
                            <ChatSection
                              user={user}
                              onLogin={() => setShowLoginModal(true)}
                            />
                          </Suspense>
                        )}
                      </div>
                    </div>
                  </section>
                </div>

                {/* 工具下载 */}
                <div id="downloads">
                  <Suspense fallback={<LoadingPlaceholder />}>
                    <DownloadsSection 
                      user={user} 
                      onLogin={() => setShowLoginModal(true)}
                    />
                  </Suspense>
                </div>

                {/* 文档中心 */}
                <div id="docs">
                  <Suspense fallback={<LoadingPlaceholder />}>
                    <DocumentationSection />
                  </Suspense>
                </div>

                {/* 最新动态 */}
                <div id="news">
                  <Suspense fallback={<LoadingPlaceholder />}>
                    <NewsSection />
                  </Suspense>
                </div>
              </main>

              {/* 页脚 */}
              <Footer />
              
              {/* 登录模态框 */}
              {showLoginModal && (
                <Suspense fallback={<div>Loading...</div>}>
                  <LoginModal 
                    isOpen={showLoginModal}
                    onLogin={handleLogin}
                    onClose={() => setShowLoginModal(false)}
                  />
                </Suspense>
              )}
            </div>
        )}
      </div>
    </ErrorBoundary>
  );
}

// 应用图标降级功能
export default withIconFallback(App);