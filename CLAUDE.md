# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Full Platform Deployment (Recommended)
Due to external service integrations (OpenVSCode, LobeChat, LiteLLM), complete deployment requires Docker Compose:

```bash
# Complete development/deployment workflow
docker-compose down -v && npm run build && docker-compose build --no-cache && docker-compose up -d

# Individual steps breakdown:
docker-compose down -v        # Stop and remove containers with volumes
npm run build                 # Build optimized frontend
docker-compose build --no-cache  # Rebuild all services from scratch
docker-compose up -d          # Start all services in background
```

This command sequence ensures:
- Clean environment (removes old containers/volumes)
- Fresh frontend build with latest changes  
- Rebuilt Docker images without cache conflicts
- All integrated services running (OpenVSCode, LobeChat, LiteLLM, LDAP auth)

### Frontend-Only Development
For frontend-only changes without external services:

```bash
# Development with LDAP server only
npm run dev:full               # Start both frontend (5173) and LDAP auth server (3002)
npm run dev                    # Frontend only (localhost:5173)
npm run dev:host               # Frontend accessible on network (0.0.0.0:5173)
npm run dev:network           # Both services accessible on network
npm run ldap                  # LDAP auth server only (3002)

# Building and Preview
npm run build                 # Production build with optimizations
npm run build:verbose         # Build with detailed output
npm run build:staging         # Staging build
npm run build:intranet        # Intranet deployment build
npm run preview               # Preview production build (localhost:4173)
npm run preview:host          # Preview accessible on network

# Code Quality
npm run lint                  # ESLint code checking
```

### Configuration Management
```bash
npm run config:setup          # Copy env.example to .env
npm run config:check          # Check for hardcoded configurations
npm run config:validate       # Validate environment configuration

# Intranet Deployment
npm run intranet:check        # Check external dependencies
npm run intranet:prepare      # Prepare for intranet deployment
npm run intranet:localize     # Localize external resources
npm run intranet:restore      # Restore original files
npm run verify:intranet       # Verify intranet deployment
```

### Performance and Optimization
```bash
npm run optimize              # Run all optimizations
npm run optimize:images       # Optimize images
npm run optimize:build        # Optimize and build
```

## Architecture Overview

### Frontend Architecture
- **Framework**: React 19.1 with Vite 6.3.5 build system
- **State Management**: React hooks with custom service layer for persistent state
- **Styling**: Tailwind CSS 3.4.17 with atomic design approach
- **Authentication**: LDAP-based authentication with JWT tokens and auto-logout
- **Component Strategy**: Lazy loading for non-critical sections to optimize bundle size

### Key Components Structure
```
src/components/
├── AdaptivePixelLoader.jsx    # Intelligent loading animation
├── APIKeySection.jsx          # API key management with caching
├── ChatSection.jsx            # LobeChat integration
├── DocumentationSection.jsx   # Documentation with search (optimized)
├── DownloadsSection.jsx       # Tool downloads
├── ErrorBoundary.jsx          # Error handling wrapper
├── FeaturesGuideSection.jsx   # Platform metrics and features
├── Footer.jsx / Navbar.jsx    # Layout components
├── HeroSection.jsx            # Landing section
├── LoginModal.jsx             # LDAP authentication UI
├── NewsSection.jsx            # Technical news
└── WebIDESection.jsx          # VSCode integration
```

### Service Layer Architecture
```
src/services/
├── apiKeyService.js           # API key CRUD with intelligent caching
├── authManager.js             # Authentication state with periodic checks
├── chatAuthService.js         # Chat session management
├── documentService.js         # Document management
├── ldapService.js             # LDAP authentication client
├── metricsService.js          # Platform metrics with fallback
├── AppPreloader.js            # Application preloading
└── pageContentCollector.js    # Content indexing
```

### Backend Services
```
src/server/
├── ldapAuthServer.js          # Express LDAP authentication server (port 3002)
├── ldapConfig.js              # LDAP connection configurations
└── /config/apiConfig.js       # API endpoint configurations
```

### Key Features
1. **Multi-Environment LDAP Support**: Supports 240.10 and 242.2 cloud desktop environments
2. **Intelligent Caching**: Services implement smart caching with TTL to reduce API calls
3. **Performance Optimizations**: Component-level memoization, lazy loading, and optimized bundle splitting
4. **Intranet Deployment**: Complete localization of external resources for offline deployment
5. **Error Recovery**: Graceful degradation with fallback mechanisms throughout

## Configuration System

### Environment Variables
The project uses a comprehensive environment configuration system. Key variables:

#### LDAP Authentication
```bash
# Multi-environment support
LDAP_240.10云桌面环境_URL=ldap://**************:389
LDAP_240.10云桌面环境_BASE_DN=DC=DEVVDI,DC=YNRCC,DC=COM
LDAP_242.2云桌面环境_URL=ldap://*************:389
LDAP_242.2云桌面环境_BASE_DN=DC=VDI,DC=YNNX,DC=COM
```

#### Service Endpoints
```bash
VITE_LDAP_API_URL=http://localhost:3002        # LDAP auth service
VITE_LITELLM_API_BASE=http://localhost:4000    # LiteLLM API
VITE_LOBECHAT_BASE_URL=http://localhost:3210   # LobeChat integration
VITE_OPENVSCODE_BASE_URL=http://localhost:3667 # VSCode Web IDE
```

### Test Accounts
Pre-configured test users for development:
- `zhangsan` / `zhangsan123` (张三 - 信息技术部)
- `lisi` / `lisi456` (李四 - 研发部) 
- `admin` / `admin123` (系统管理员)
- `developer` / `dev123456` (开发工程师)

## Critical Performance Considerations

### Bundle Optimization
- **Code Splitting**: Aggressive chunking by component type and vendor libraries
- **Lazy Loading**: All non-critical components are lazy-loaded
- **Vendor Chunking**: Separate chunks for React, icons, highlighting, markdown, etc.

### Service Performance
- **API Key Service**: 2-minute user info cache, 1-minute key cache, 5-minute connection cache
- **Metrics Service**: 10-minute polling with intelligent fallback to mock data
- **Auth Manager**: Periodic 5-minute auth status checks with 3-day timeout

### Memory Management
- **Service Worker**: POST requests bypass caching to prevent Cache API errors
- **Error Boundaries**: Component-level error isolation
- **Cleanup**: Proper cleanup of intervals, timeouts, and event listeners

## Development Guidelines

### Service Layer Patterns
- All services implement caching with TTL expiration
- Network requests include timeout and retry logic
- Fallback mechanisms for when external services are unavailable
- Consistent error handling and logging patterns

### Component Patterns
- Use `React.memo()` for expensive components
- Implement `useMemo()` and `useCallback()` for performance-critical operations
- Lazy load heavy components using `React.lazy()`
- Error boundaries around potentially failing components

### Build and Deployment
- **Full Platform**: Use `docker-compose down -v && npm run build && docker-compose build --no-cache && docker-compose up -d` for complete environment
- **Frontend Only**: `npm run dev:full` for frontend + LDAP auth server only
- **Production**: `npm run build` creates optimized production build (required before Docker Compose)
- **Intranet**: Use intranet-specific commands for air-gapped deployments
- **Testing**: Always run `npm run lint` before committing

### Performance Testing
- Use browser dev tools to verify bundle sizes remain under 800KB per chunk
- Monitor network requests to ensure caching is working effectively
- Test on lower-end devices to verify performance optimizations

## External Service Integrations

### LobeChat Integration
- Embedded via secure iframe with nginx proxy
- Single sign-on with LDAP authentication 
- User session isolation and real-time WebSocket support

### LiteLLM API Integration
- Supports multiple AI models (qwen3-235b-a22b, Claude, etc.)
- Master key authentication with token-based access
- Real-time metrics collection with graceful fallbacks

### VSCode Web IDE
- Module federation setup for seamless integration
- Nginx proxy configuration for security
- AI code assistant integration

This platform is designed for Yunnan Rural Credit Cooperative's internal development team, focusing on AI-assisted development tools with enterprise-grade security and performance.